using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using TacticalCombatSystem.Interfaces;
using TacticalCombatSystem.Interfaces.Core;
using TacticalCombatSystem.Status;
using ICombatParticipant = TacticalCombatSystem.Interfaces.ICombatParticipant;
using ICombatAction = TacticalCombatSystem.Interfaces.ICombatAction;

namespace TacticalCombatSystem.Characters
{
    /// <summary>
    /// Base class for all characters in the game
    /// </summary>
    public class BaseCharacter : MonoBehaviour, ICombatParticipant
    {
        // ICombatParticipant implementation
        public string ParticipantName => characterData?.CharacterName ?? "Unknown";
        
        public int CurrentHP 
        { 
            get => currentHP;
            set => currentHP = Mathf.Clamp(value, 0, MaxHP);
        }
        
        public int MaxHP => characterData?.MaxHealth ?? 0;
        
        public int CurrentMP 
        { 
            get => currentMP;
            set => currentMP = Mathf.Clamp(value, 0, MaxMP);
        }
        
        public int MaxMP => characterData?.MaxMana ?? 0;
        
        public int Attack => characterData?.Attack ?? 0;
        public int Defense => characterData?.Defense ?? 0;
        public int MagicAttack => characterData?.MagicAttack ?? 0;
        public int MagicDefense => characterData?.MagicDefense ?? 0;
        public int Speed => characterData?.Speed ?? 0;
        public float CriticalChance => characterData?.CriticalChance ?? 0f;
        public float CriticalMultiplier => characterData?.CriticalMultiplier ?? 1.5f;
        public bool IsAlive => CurrentHP > 0;
        public bool IsPlayerControlled { get; private set; }
        [Header("Character Data")]
        [SerializeField] private Character characterData;
        [SerializeField] private bool isPlayerControlled = false;
        
        // Runtime stats (initialized from characterData)
        private int currentHP;
        private int currentMP;
        private readonly Dictionary<StatType, float> statModifiers = new Dictionary<StatType, float>();
        
        // Status effects
        private readonly List<StatusEffect> activeStatusEffects = new List<StatusEffect>();
        private readonly Dictionary<Type, StatusEffect> statusEffectLookup = new Dictionary<Type, StatusEffect>();
        
        // Combat actions
        private readonly List<ICombatAction> availableActions = new List<ICombatAction>();
        
        // References
        private BattleCharacter battleCharacter;
        private Animator characterAnimator;
        
        // ICombatParticipant implementation
        public List<ICombatAction> GetAvailableActions()
        {
            return new List<ICombatAction>(availableActions);
        }
        
        public bool CanPerformAction(ICombatAction action)
        {
            if (action == null) return false;
            return action.CanBeUsedBy(this);
        }
        
        public void ApplyStatusEffect(StatusEffect effect)
        {
            if (effect == null) return;
            
            var effectType = effect.GetType();
            if (statusEffectLookup.TryGetValue(effectType, out var existingEffect))
            {
                // Stack or refresh the existing effect
                existingEffect.RefreshDuration();
            }
            else
            {
                // Add new effect
                var newEffect = Instantiate(effect);
                activeStatusEffects.Add(newEffect);
                statusEffectLookup[effectType] = newEffect;
                newEffect.OnApplied(this);
                OnStatusEffectApplied(newEffect);
            }
        }
        

        
        public bool HasStatusEffect(string effectId)
        {
            return activeStatusEffects.Exists(e => e.EffectId == effectId);
        }
        
        public List<StatusEffect> GetStatusEffects()
        {
            return new List<StatusEffect>(activeStatusEffects);
        }
        
        public bool CanTarget(ICombatParticipant target)
        {
            if (target == null) return false;
            return target.IsAlive;
        }
        
        public List<ICombatParticipant> GetValidTargets(ICombatAction action)
        {
            var targets = new List<ICombatParticipant>();
            if (action == null) return targets;

            // Find the battle manager in the scene
            var battleManager = FindObjectOfType<MonoBehaviour>() as IBattleManager;
            if (battleManager != null)
            {
                targets.AddRange(battleManager.GetPlayerTeam().FindAll(c => c.IsAlive));
                targets.AddRange(battleManager.GetEnemyTeam().FindAll(c => c.IsAlive));
            }

            return targets.FindAll(t => action.IsValidTarget(this, t));
        }
        
        public void OnTurnStart()
        {
            // Process start of turn status effects
            foreach (var effect in activeStatusEffects.ToArray())
            {
                effect.OnTurnStart(this);
                
                // Remove expired effects
                if (effect.IsExpired)
                {
                    RemoveStatusEffect(effect);
                }
            }
            
            // Notify listeners
            OnTurnStarted?.Invoke();
        }
        
        public void OnTurnEnd()
        {
            // Process end of turn status effects
            foreach (var effect in activeStatusEffects.ToArray())
            {
                effect.OnTurnEnd(this);
                
                // Remove expired effects
                if (effect.IsExpired)
                {
                    RemoveStatusEffect(effect);
                }
            }
            
            // Notify listeners
            OnTurnEnded?.Invoke();
        }
        
        public void OnActionPerformed(ICombatAction action)
        {
            // Process action performed status effects
            foreach (var effect in activeStatusEffects.ToArray())
            {
                effect.OnActionPerformed(this, action);
            }
            
            // Notify listeners
            ActionPerformed?.Invoke(action);
        }
        
        public void OnDamageTaken(int amount, ICombatParticipant source)
        {
            if (amount <= 0) return;
            
            // Apply damage
            CurrentHP -= amount;
            
            // Process damage taken status effects
            foreach (var effect in activeStatusEffects.ToArray())
            {
                effect.OnDamageTaken(this, source, ref amount);
            }
            
            // Notify listeners
            DamageTaken?.Invoke(amount, source);
            
            // Check for death
            if (!IsAlive)
            {
                OnDeath();
            }
        }
        
        public void OnHealed(int amount, ICombatParticipant source)
        {
            if (amount <= 0) return;
            
            // Apply healing
            int oldHP = CurrentHP;
            CurrentHP += amount;
            int actualHeal = CurrentHP - oldHP;
            
            // Process healing received status effects
            foreach (var effect in activeStatusEffects.ToArray())
            {
                effect.OnHealingReceived(this, source, ref actualHeal);
            }
            
            // Notify listeners
            if (actualHeal > 0)
            {
                HealingReceived?.Invoke(actualHeal, source);
            }
        }
        
        public void OnStatusEffectApplied(StatusEffect effect)
        {
            StatusEffectApplied?.Invoke(effect);
        }
        
        public void OnStatusEffectRemoved(StatusEffect effect)
        {
            StatusEffectRemoved?.Invoke(effect);
        }
        
        private void OnDeath()
        {
            // Process death status effects
            foreach (var effect in activeStatusEffects.ToArray())
            {
                effect.OnDeath(this);
            }
            
            // Clear all status effects
            activeStatusEffects.Clear();
            statusEffectLookup.Clear();
            
            // Notify listeners
            Died?.Invoke();
        }
        
        // Events
        public event Action TurnStarted;
        public event Action TurnEnded;
        public event Action<ICombatAction> ActionPerformed;
        public event Action<int, ICombatParticipant> DamageTaken;
        public event Action<int, ICombatParticipant> HealingReceived;
        public event Action<StatusEffect> StatusEffectApplied;
        public event Action<StatusEffect> StatusEffectRemoved;
        public event Action Died;
        
        // ICombatParticipant implementation - Using the one defined above with expression-bodied members
        public Character CharacterData => characterData;
        public Animator CharacterAnimator => characterAnimator;
        
        // Events
        public event System.Action<BaseCharacter> OnHealthChanged;
        public event System.Action<BaseCharacter> OnManaChanged;
        public event System.Action<BaseCharacter> OnStatusEffectAdded;
        public event System.Action<BaseCharacter> OnStatusEffectRemoved;
        
        private void Awake()
        {
            // Initialize stats from character data
            if (characterData != null)
            {
                CurrentHP = characterData.MaxHealth;
                CurrentMP = characterData.MaxMana;
                
                // Initialize available actions from character data
                foreach (var ability in characterData.Abilities)
                {
                    availableActions.Add(ability);
                }
            }
            characterAnimator = GetComponentInChildren<Animator>();
            battleCharacter = GetComponent<BattleCharacter>();
            
            // Initialize from character data if available
            if (characterData != null)
            {
                InitializeFromCharacterData(characterData, isPlayerControlled);
            }
        }
        
        /// <summary>
        /// Initialize this character with data from a Character ScriptableObject
        /// </summary>
        public void InitializeFromCharacterData(Character data, bool isPlayerControlled)
        {
            if (data == null) return;
            
            characterData = data;
            this.isPlayerControlled = isPlayerControlled;
            
            // Initialize stats
            currentHP = data.MaxHealth;
            currentMP = data.MaxMana;

            // Set up animator if available
            if (characterAnimator != null && data.AnimatorController != null)
            {
                characterAnimator.runtimeAnimatorController = data.AnimatorController;
            }

            // Apply any starting status effects
            foreach (var effect in data.StartingStatusEffects)
            {
                if (effect != null)
                {
                    ApplyStatusEffect(effect);
                }
            }
            
            // Notify listeners
            OnHealthChanged?.Invoke(this);
            OnManaChanged?.Invoke(this);
        }
        
        private void SetCurrentHP(int value)
        {
            int newHP = Mathf.Clamp(value, 0, MaxHP);
            if (currentHP != newHP)
            {
                currentHP = newHP;
                OnHealthChanged?.Invoke(this);
                
                if (currentHP <= 0)
                {
                    // Handle character death
                    HandleDeath();
                }
            }
        }

        #region ICombatParticipant Implementation

        public bool CanPerformAction(ICombatAction action)
        {
            if (action == null) return false;

            // Check if we have enough MP/HP for the action
            // Note: We'll need to add cost properties to ICombatAction interface
            // For now, just return true if action is not null
            return true;
        }
        
        /// <summary>
        /// Gets the current value of a stat, including all modifiers
        /// </summary>
        public int GetStat(StatType statType)
        {
            if (characterData == null) return 0;
            
            float baseValue = 0;
            
            // Get base value from character data
            switch (statType)
            {
                case StatType.Health:
                    return currentHP;
                case StatType.MaxHealth:
                    baseValue = characterData.maxHealth;
                    break;
                case StatType.Mana:
                    return currentMP;
                case StatType.MaxMana:
                    baseValue = characterData.maxMana;
                    break;
                case StatType.Attack:
                    baseValue = characterData.attack;
                    break;
                case StatType.Defense:
                    baseValue = characterData.defense;
                    break;
                case StatType.MagicAttack:
                    baseValue = characterData.magicAttack;
                    break;
                case StatType.MagicDefense:
                    baseValue = characterData.magicDefense;
                    break;
                case StatType.Speed:
                    baseValue = characterData.speed;
                    break;
                default:
                    Debug.LogWarning($"Unhandled stat type: {statType}");
                    return 0;
            }
            
            // Apply stat modifiers
            if (statModifiers.TryGetValue(statType, out float modifier))
            {
                baseValue += modifier;
            }
            
            // Ensure stat doesn't go below 1 (except for HP/MP which are handled separately)
            if (statType != StatType.Health && statType != StatType.Mana)
            {
                baseValue = Mathf.Max(1, baseValue);
            }
            
            return Mathf.FloorToInt(baseValue);
        }
        
        /// <summary>
        /// Adds a modifier to a stat
        /// </summary>
        public void AddStatModifier(StatModifier modifier)
        {
            if (modifier == null) return;
            
            if (statModifiers.ContainsKey(modifier.statType))
            {
                statModifiers[modifier.statType] += modifier.value;
            }
            else
            {
                statModifiers[modifier.statType] = modifier.value;
            }
            
            // Update any affected stats
            if (modifier.statType == StatType.MaxHealth)
            {
                // Adjust current HP proportionally
                float healthRatio = (float)currentHP / MaxHP;
                currentHP = Mathf.FloorToInt(GetStat(StatType.MaxHealth) * healthRatio);
                OnHealthChanged?.Invoke(this);
            }
        }
        
        /// <summary>
        /// Removes a modifier from a stat
        /// </summary>
        public void RemoveStatModifier(StatModifier modifier)
        {
            if (modifier == null) return;
            
            if (statModifiers.ContainsKey(modifier.statType))
            {
                statModifiers[modifier.statType] -= modifier.value;
                
                // Remove the entry if it's now zero
                if (Mathf.Approximately(statModifiers[modifier.statType], 0f))
                {
                    statModifiers.Remove(modifier.statType);
                }
                
                // Update any affected stats
                if (modifier.statType == StatType.MaxHealth)
                {
                    // Ensure current HP doesn't exceed new max
                    currentHP = Mathf.Min(currentHP, GetStat(StatType.MaxHealth));
                    OnHealthChanged?.Invoke(this);
                }
            }
        }

        public void ApplyStatusEffect(object effect)
        {
            if (effect == null) return;

            // For now, we'll handle this generically since the interface uses object
            // In a real implementation, you'd cast to the appropriate status effect type
            Debug.Log($"{ParticipantName} gained status effect: {effect.GetType().Name}");
        }

        public void RemoveStatusEffect(object effect)
        {
            if (effect == null) return;

            // For now, we'll handle this generically since the interface uses object
            Debug.Log($"{ParticipantName} lost status effect: {effect.GetType().Name}");
        }

        public bool HasStatusEffect(string effectId)
        {
            return activeStatusEffects.Exists(e => e.statusName == effectId);
        }

        public List<object> GetStatusEffects()
        {
            // Convert to object list to match interface
            return new List<object>(activeStatusEffects.Cast<object>());
        }

        public bool CanTarget(ICombatParticipant target)
        {
            if (target == null) return false;
            
            // By default, can target any alive participant
            return target.IsAlive;
        }

        public List<ICombatParticipant> GetValidTargets(ICombatAction action)
        {
            // This should be implemented based on the current battle state
            // For now, return an empty list
            return new List<ICombatParticipant>();
        }

        public void OnTurnStart()
        {
            // Process status effects at the start of the turn
            for (int i = activeStatusEffects.Count - 1; i >= 0; i--)
            {
                var effect = activeStatusEffects[i];
                effect.OnTurnStart(this);
                
                // Remove expired effects
                if (effect.IsExpired())
                {
                    RemoveStatusEffect(effect);
                }
            }
        }

        public void OnTurnEnd()
        {
            // Process status effects at the end of the turn
            for (int i = activeStatusEffects.Count - 1; i >= 0; i--)
            {
                var effect = activeStatusEffects[i];
                effect.OnTurnEnd(this);
                
                // Remove expired effects
                if (effect.IsExpired())
                {
                    RemoveStatusEffect(effect);
                }
            }
            
            // Reduce cooldowns on abilities
            foreach (var action in availableActions)
            {
                action.ReduceCooldown();
            }
        }

        public void OnActionPerformed(ICombatAction action)
        {
            // Apply action costs
            if (action != null)
            {
                // Note: We'll need to add cost properties to ICombatAction interface
                // For now, just log the action
                Debug.Log($"{ParticipantName} performed action: {action.GetType().Name}");
            }
        }

        public void OnDamageTaken(int amount, ICombatParticipant source)
        {
            if (!IsAlive) return;
            
            // Apply damage
            CurrentHP -= amount;
            
            // Visual/audio feedback would go here
            
            // Check for death
            if (CurrentHP <= 0)
            {
                CurrentHP = 0;
                // Handle death
            }
        }

        public void OnHealed(int amount, ICombatParticipant source)
        {
            if (!IsAlive) return;
            
            // Apply healing
            int oldHP = CurrentHP;
            CurrentHP += amount;
            int actualHeal = CurrentHP - oldHP;
            
            // Visual/audio feedback would go here
        }

        public void OnStatusEffectApplied(object effect)
        {
            // Visual/audio feedback would go here
            Debug.Log($"{ParticipantName} gained {effect.GetType().Name} effect");
        }

        public void OnStatusEffectRemoved(object effect)
        {
            // Visual/audio feedback would go here
            Debug.Log($"{ParticipantName} lost {effect.GetType().Name} effect");
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Add an action to this character's available actions
        /// </summary>
        public void AddAction(CombatAction action)
        {
            if (action != null && !availableActions.Contains(action))
            {
                availableActions.Add(action);
            }
        }

        /// <summary>
        /// Remove an action from this character's available actions
        /// </summary>
        public void RemoveAction(CombatAction action)
        {
            if (action != null)
            {
                availableActions.Remove(action);
            }
        }

        #endregion
        
        #region Combat Actions
        
        /// <summary>
        /// Makes the character take damage, applying defense calculations
        /// </summary>
        public int TakeDamage(int baseDamage, bool isMagical = false)
        {
            if (!IsAlive) return 0;
            
            // Calculate damage after defense
            int defense = isMagical ? MagicDefense : Defense;
            int damage = Mathf.Max(1, baseDamage - defense);
            
            // Apply damage
            CurrentHP -= damage;
            
            // Show damage number if this is a battle character
            battleCharacter?.ShowDamageNumber(damage);
            
            // Trigger hit animation
            characterAnimator?.SetTrigger("Hit");
            
            Debug.Log($"{ParticipantName} took {damage} damage!");
            
            return damage;
        }
        
        /// <summary>
        /// Heals the character by the specified amount
        /// </summary>
        public int Heal(int amount)
        {
            if (!IsAlive) return 0;
            
            int healAmount = Mathf.Min(amount, MaxHP - CurrentHP);
            CurrentHP += healAmount;
            
            // Show heal number if this is a battle character
            battleCharacter?.ShowHealNumber(healAmount);
            
            Debug.Log($"{ParticipantName} healed for {healAmount} HP!");
            
            return healAmount;
        }
        
        /// <summary>
        /// Spends mana points
        /// </summary>
        public bool SpendMana(int amount)
        {
            if (CurrentMP < amount) return false;
            
            CurrentMP -= amount;
            return true;
        }
        
        /// <summary>
        /// Restores mana points
        /// </summary>
        public int RestoreMana(int amount)
        {
            int restoreAmount = Mathf.Min(amount, MaxMP - CurrentMP);
            CurrentMP += restoreAmount;
            return restoreAmount;
        }
        
        #endregion
        
        #endregion
        
        #region Private Methods
        
        private void HandleDeath()
        {
            // Trigger death animation
            characterAnimator?.SetTrigger("Die");
            
            // Notify listeners
            // TODO: Add OnDeath event
            
            Debug.Log($"{ParticipantName} has been defeated!");
            
            // Disable components that shouldn't be active when dead
            var collider = GetComponent<Collider>();
            if (collider != null) collider.enabled = false;
            
            // If this is a battle character, handle battle-specific death logic
            battleCharacter?.OnDeath();
        }
        
        #endregion
    }
}
