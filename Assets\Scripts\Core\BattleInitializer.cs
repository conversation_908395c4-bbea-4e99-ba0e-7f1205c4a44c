using UnityEngine;
using System.Collections.Generic;
using TacticalCombatSystem.Interfaces;
using TacticalCombatSystem.Interfaces.Core;

namespace TacticalCombatSystem.Core
{
    public class BattleInitializer : MonoBehaviour
    {
        [Header("Scene References")]
        public IBattleManager battleManager;
        public IBattleUI battleUI;
        public BattleInputHandler inputHandler;
        public Camera battleCamera;
        
        [Header("Character Prefabs")]
        public GameObject[] playerPrefabs;
        public GameObject[] enemyPrefabs;
        
        [Header("Spawn Points")]
        public Transform[] playerSpawnPoints;
        public Transform[] enemySpawnPoints;
        
        [Header("Test Data")]
        public ICharacter[] testPlayerCharacters;
        public ICharacter[] testEnemyCharacters;
        
        private void Awake()
        {
            // Find components in the scene
            if (battleManager == null)
            {
                battleManager = FindObjectOfType<MonoBehaviour>() as IBattleManager;
            }

            if (battleUI == null)
            {
                var battleUIComponent = FindObjectOfType<MonoBehaviour>() as IBattleUI;
                if (battleUIComponent != null)
                    battleUI = battleUIComponent;
            }

            if (inputHandler == null)
            {
                inputHandler = FindObjectOfType<BattleInputHandler>();
            }

            // Set up camera reference
            if (battleCamera == null)
            {
                battleCamera = Camera.main;
                if (battleCamera == null)
                {
                    Debug.LogError("No main camera found in the scene!");
                }
            }

            // Initialize the input handler
            if (inputHandler != null)
            {
                inputHandler.battleCamera = battleCamera;
                inputHandler.characterLayer = LayerMask.GetMask("Characters");
            }

            // Set up test teams if we have test characters
            if (testPlayerCharacters != null && testPlayerCharacters.Length > 0 &&
                testEnemyCharacters != null && testEnemyCharacters.Length > 0)
            {
                SetupTestBattle();
            }
            else
            {
                // Otherwise, spawn prefabs
                SpawnCharacters();
            }
        }
        
        private void SetupTestBattle()
        {
            // Add test player characters using interface methods
            foreach (ICharacter character in testPlayerCharacters)
            {
                if (character != null)
                {
                    // We need to convert ICharacter to ICombatParticipant
                    // This will need to be handled by the BattleManager implementation
                    Debug.Log($"Adding player character: {character.CharacterName}");
                }
            }

            // Add test enemy characters
            foreach (ICharacter character in testEnemyCharacters)
            {
                if (character != null)
                {
                    Debug.Log($"Adding enemy character: {character.CharacterName}");
                }
            }

            Debug.Log("Test battle setup completed");
        }
        
        private void SpawnCharacters()
        {
            if (playerPrefabs == null || playerPrefabs.Length == 0)
            {
                Debug.LogError("No player prefabs assigned to spawn!");
                return;
            }

            if (enemyPrefabs == null || enemyPrefabs.Length == 0)
            {
                Debug.LogError("No enemy prefabs assigned to spawn!");
                return;
            }

            // Spawn player characters
            for (int i = 0; i < playerPrefabs.Length && i < playerSpawnPoints.Length; i++)
            {
                if (playerPrefabs[i] != null && playerSpawnPoints[i] != null)
                {
                    GameObject playerObj = Instantiate(playerPrefabs[i], playerSpawnPoints[i].position, playerSpawnPoints[i].rotation);
                    var combatParticipant = playerObj.GetComponent<ICombatParticipant>();

                    if (combatParticipant != null)
                    {
                        battleManager.AddToPlayerTeam(combatParticipant);
                    }
                }
            }

            // Spawn enemy characters
            for (int i = 0; i < enemyPrefabs.Length && i < enemySpawnPoints.Length; i++)
            {
                if (enemyPrefabs[i] != null && enemySpawnPoints[i] != null)
                {
                    GameObject enemyObj = Instantiate(enemyPrefabs[i], enemySpawnPoints[i].position, enemySpawnPoints[i].rotation);
                    var combatParticipant = enemyObj.GetComponent<ICombatParticipant>();

                    if (combatParticipant != null)
                    {
                        battleManager.AddToEnemyTeam(combatParticipant);
                    }
                }
            }

            Debug.Log("Character spawning completed");
        }
        
        private void OnValidate()
        {
            // Ensure we have enough spawn points for the test characters
            if (testPlayerCharacters != null && testPlayerCharacters.Length > 0 && 
                (playerSpawnPoints == null || playerSpawnPoints.Length < testPlayerCharacters.Length))
            {
                Debug.LogWarning($"Not enough player spawn points for all test characters. Need {testPlayerCharacters.Length} but have {playerSpawnPoints?.Length ?? 0}.");
            }
            
            if (testEnemyCharacters != null && testEnemyCharacters.Length > 0 && 
                (enemySpawnPoints == null || enemySpawnPoints.Length < testEnemyCharacters.Length))
            {
                Debug.LogWarning($"Not enough enemy spawn points for all test characters. Need {testEnemyCharacters.Length} but have {enemySpawnPoints?.Length ?? 0}.");
            }
        }
    }
}
