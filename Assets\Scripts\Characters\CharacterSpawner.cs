using UnityEngine;
using System.Collections.Generic;
using TacticalCombatSystem.Interfaces;
using TacticalCombatSystem.Interfaces.Core;

namespace TacticalCombatSystem.Characters
{
    public class CharacterSpawner : MonoBehaviour
    {
        [System.Serializable]
        public class SpawnPoint
        {
            public Transform transform;
            public bool isPlayerTeam;
        }

        [Header("References")]
        public GameObject characterPrefab;
        public List<SpawnPoint> spawnPoints = new List<SpawnPoint>();
        
        [Header("Test Data")]
        public List<Character> testPlayerTeam = new List<Character>();
        public List<Character> testEnemyTeam = new List<Character>();

        private void Start()
        {
            // Find the battle manager in the scene
            var battleManager = FindObjectOfType<MonoBehaviour>() as IBattleManager;
            if (battleManager != null && testPlayerTeam.Count > 0)
            {
                foreach (var character in testPlayerTeam)
                {
                    battleManager.AddToPlayerTeam(character);
                }

                foreach (var character in testEnemyTeam)
                {
                    battleManager.AddToEnemyTeam(character);
                }
            }

            SpawnTeams();
        }
        
        public void SpawnTeams()
        {
            if (characterPrefab == null)
            {
                Debug.LogError("Character prefab is not assigned!");
                return;
            }

            var battleManager = FindObjectOfType<MonoBehaviour>() as IBattleManager;
            if (battleManager == null)
            {
                Debug.LogError("BattleManager not found in the scene!");
                return;
            }

            // Clear existing characters
            foreach (var spawnPoint in spawnPoints)
            {
                if (spawnPoint.transform.childCount > 0)
                {
                    for (int i = spawnPoint.transform.childCount - 1; i >= 0; i--)
                    {
                        Destroy(spawnPoint.transform.GetChild(i).gameObject);
                    }
                }
            }

            // Get teams from battle manager
            var playerTeam = battleManager.GetPlayerTeam();
            var enemyTeam = battleManager.GetEnemyTeam();

            // Spawn player team
            for (int i = 0; i < playerTeam.Count; i++)
            {
                var spawnPoint = GetSpawnPoint(true, i);
                if (spawnPoint != null && playerTeam[i] is Character character)
                {
                    SpawnCharacter(character, spawnPoint, true);
                }
            }

            // Spawn enemy team
            for (int i = 0; i < enemyTeam.Count; i++)
            {
                var spawnPoint = GetSpawnPoint(false, i);
                if (spawnPoint != null && enemyTeam[i] is Character character)
                {
                    SpawnCharacter(character, spawnPoint, false);
                }
            }
        }
        
        private Transform GetSpawnPoint(bool isPlayerTeam, int index)
        {
            var teamSpawnPoints = spawnPoints.FindAll(p => p.isPlayerTeam == isPlayerTeam);
            if (teamSpawnPoints.Count == 0)
            {
                Debug.LogError($"No spawn points found for { (isPlayerTeam ? "player" : "enemy") } team!");
                return null;
            }
            
            // Use modulo to loop through available spawn points if there are more characters than spawn points
            return teamSpawnPoints[Mathf.Min(index, teamSpawnPoints.Count - 1)].transform;
        }
        
        private void SpawnCharacter(Character characterData, Transform spawnPoint, bool isPlayerTeam)
        {
            if (characterData == null || spawnPoint == null) return;
            
            // Instantiate character
            var characterObj = Instantiate(characterPrefab, spawnPoint.position, spawnPoint.rotation, spawnPoint);
            characterObj.name = $"Character_{characterData.characterName}";
            
            // Set up character visual
            var characterVisual = characterObj.GetComponent<CharacterVisual>();
            if (characterVisual != null)
            {
                characterVisual.Initialize(characterData, isPlayerTeam);
            }
            
            // Set up world space UI
            var canvas = characterObj.GetComponentInChildren<Canvas>();
            if (canvas != null)
            {
                // Make UI face the camera
                canvas.worldCamera = Camera.main;
                
                // For world space UI, we might want to make it face the camera at all times
                var billboard = canvas.GetComponent<Billboard>();
                if (billboard == null)
                {
                    billboard = canvas.gameObject.AddComponent<Billboard>();
                    billboard.Camera = Camera.main;
                }
            }
        }
        
        // Helper method to spawn a single character for testing
        public void SpawnTestCharacter(Character characterData, bool isPlayerTeam, int spawnIndex = 0)
        {
            var spawnPoint = GetSpawnPoint(isPlayerTeam, spawnIndex);
            if (spawnPoint != null)
            {
                SpawnCharacter(characterData, spawnPoint, isPlayerTeam);
            }
        }
    }
    
    // Simple billboard script to make UI face the camera
    public class Billboard : MonoBehaviour
    {
        public Camera Camera;
        public bool FreezeXZ = true;
        
        private void Start()
        {
            if (Camera == null)
            {
                Camera = Camera.main;
            }
        }
        
        private void LateUpdate()
        {
            if (Camera == null) return;
            
            Vector3 lookDirection = Camera.transform.forward;
            if (FreezeXZ)
            {
                lookDirection.y = 0;
                lookDirection.Normalize();
                lookDirection = Quaternion.LookRotation(Vector3.forward, Vector3.up) * lookDirection;
            }
            
            transform.rotation = Quaternion.LookRotation(lookDirection);
        }
    }
}
