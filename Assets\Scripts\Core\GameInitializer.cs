using UnityEngine;
using System.Collections.Generic;
using TacticalCombatSystem.UI;
using TacticalCombatSystem.Interfaces;
using TacticalCombatSystem.Battle;

namespace TacticalCombatSystem.Core
{
    public class GameInitializer : MonoBehaviour
    {
        [Header("Character Data")]
        public List<ICombatParticipant> testPlayerTeam = new List<ICombatParticipant>();
        public List<ICombatParticipant> testEnemyTeam = new List<ICombatParticipant>();

        [Header("UI References")]
        public BattleUI battleUI;
        
        private void Start()
        {
            // Make sure we have a BattleManager
            var battleManager = BattleManager.Instance;
            if (battleManager == null)
            {
                var go = new GameObject("BattleManager");
                battleManager = go.AddComponent<BattleManager>();
            }

            // Set up teams
            foreach (var character in testPlayerTeam)
            {
                if (character != null)
                {
                    battleManager.AddToPlayerTeam(character);
                }
            }

            foreach (var character in testEnemyTeam)
            {
                if (character != null)
                {
                    battleManager.AddToEnemyTeam(character);
                }
            }

            // Initialize UI if available
            if (battleUI != null)
            {
                // For now, we'll need to find a different way to initialize the UI
                // since we can't directly convert ICombatParticipant to BaseCharacter
                // without creating a circular dependency
                Debug.Log("BattleUI initialization needs to be handled differently to avoid circular dependencies");
                // TODO: Refactor BattleUI to work with ICombatParticipant instead of BaseCharacter
            }

            // Start the battle
            battleManager.StartBattle();
        }
    }
}
