using UnityEngine;
using System.Collections.Generic;
using TacticalCombatSystem.Interfaces;
using TacticalCombatSystem.Interfaces.Core;

namespace TacticalCombatSystem.Core
{
    public class GameInitializer : MonoBehaviour
    {
        [Header("Character Data")]
        public List<ICombatParticipant> testPlayerTeam = new List<ICombatParticipant>();
        public List<ICombatParticipant> testEnemyTeam = new List<ICombatParticipant>();

        [Header("UI References")]
        public IBattleUI battleUI;
        
        private void Start()
        {
            // Find the BattleManager in the scene
            var battleManager = FindObjectOfType<MonoBehaviour>() as IBattleManager;
            if (battleManager == null)
            {
                Debug.LogError("No BattleManager found in the scene!");
                return;
            }

            // Set up teams
            foreach (var character in testPlayerTeam)
            {
                if (character != null)
                {
                    battleManager.AddToPlayerTeam(character);
                }
            }

            foreach (var character in testEnemyTeam)
            {
                if (character != null)
                {
                    battleManager.AddToEnemyTeam(character);
                }
            }

            // Initialize UI if available
            if (battleUI != null)
            {
                Debug.Log("BattleUI will be initialized through events when battle starts");
            }

            // Start the battle
            battleManager.StartBattle();
        }
    }
}
