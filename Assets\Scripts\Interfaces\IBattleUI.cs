using System.Collections.Generic;

namespace TacticalCombatSystem.Interfaces
{
    /// <summary>
    /// Interface for battle UI components to avoid circular dependencies
    /// </summary>
    public interface IBattleUI
    {
        /// <summary>
        /// Initialize the UI with the battle teams
        /// </summary>
        void InitializeUI(List<ICombatParticipant> playerTeam, List<ICombatParticipant> enemyTeam);
        
        /// <summary>
        /// Show or hide the battle menu
        /// </summary>
        void SetBattleMenuActive(bool active);
        
        /// <summary>
        /// Update the turn display
        /// </summary>
        void UpdateTurnDisplay(string turnText);
        
        /// <summary>
        /// Add a message to the battle log
        /// </summary>
        void AddToBattleLog(string message);
        
        /// <summary>
        /// Show battle end message
        /// </summary>
        void ShowBattleEndMessage(bool playerWon);
    }
}
