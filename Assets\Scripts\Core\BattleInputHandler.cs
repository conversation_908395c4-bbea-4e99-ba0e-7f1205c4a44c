using System.Collections.Generic;
using UnityEngine;
using UnityEngine.EventSystems;
using TacticalCombatSystem.Characters;
using TacticalCombatSystem.Interfaces;
using TacticalCombatSystem.Interfaces.Core;
using ICombatParticipant = TacticalCombatSystem.Interfaces.ICombatParticipant;
using ICombatAction = TacticalCombatSystem.Interfaces.ICombatAction;

namespace TacticalCombatSystem.Core
{
    public class BattleInputHandler : MonoBehaviour
    {
        [Header("References")]
        public Camera battleCamera;
        public LayerMask characterLayer;
        public float raycastDistance = 100f;
        
        [Header("Selection")]
        public Color hoverColor = new Color(1f, 0.8f, 0f);
        public Color selectedColor = new Color(1f, 0.5f, 0f);
        
        private ICombatParticipant hoveredCharacter;
        private ICombatParticipant selectedCharacter;
        private IBattleManager battleManager;
        private ICombatAction selectedAction;
        
        private void Awake()
        {
            if (battleCamera == null)
            {
                battleCamera = Camera.main;
            }

            // Find the battle manager in the scene
            battleManager = FindObjectOfType<MonoBehaviour>() as IBattleManager;
            if (battleManager == null)
            {
                Debug.LogError("No IBattleManager found in the scene!");
            }
        }
        
        private void Update()
        {
            if (battleManager == null) return;
            
            // Check if selected character is still alive
            if (selectedCharacter != null && !selectedCharacter.IsAlive)
            {
                DeselectCharacter();
            }
            
            // Don't process input if it's not the player's turn
            if (!battleManager.IsPlayerTurn)
            {
                // Clear selection and hover when it's not the player's turn
                if (selectedCharacter != null || hoveredCharacter != null)
                {
                    DeselectCharacter();
                    UpdateHoveredCharacter(null);
                }
                return;
            }
            
            // Don't process input if the pointer is over UI
            if (EventSystem.current != null && EventSystem.current.IsPointerOverGameObject())
            {
                UpdateHoveredCharacter(null);
                return;
            }
            
            // Handle character selection
            HandleCharacterSelection();
            
            // Handle input for selected character
            if (selectedCharacter != null)
            {
                HandleSelectedCharacterInput();
            }
        }

        private void HandleCharacterSelection()
        {
            try
            {
                Ray ray = battleCamera.ScreenPointToRay(Input.mousePosition);
                RaycastHit hit;

                // Check if we're hovering over a character
                if (Physics.Raycast(ray, out hit, raycastDistance, characterLayer))
                {
                    var character = hit.collider.GetComponentInParent<BaseCharacter>();
                    if (character != null)
                    {
                        var combatParticipant = character.GetComponent<ICombatParticipant>();
                        if (combatParticipant != null && combatParticipant.IsAlive)
                        {
                            UpdateHoveredCharacter(combatParticipant);

                            // Select character on click
                            if (Input.GetMouseButtonDown(0))
                            {
                                if (selectedCharacter == null && combatParticipant.IsPlayerControlled)
                                {
                                    SelectCharacter(combatParticipant);
                                }
                                else if (selectedCharacter != null && selectedAction != null &&
                                         selectedCharacter != combatParticipant) // Don't target self unless it's a self-targeting ability
                                {
                                    // Use selected action on the clicked character if valid
                                    if (selectedAction.IsValidTarget(selectedCharacter, combatParticipant))
                                    {
                                        battleManager.UseAbility(selectedCharacter, combatParticipant, selectedAction);
                                        DeselectCharacter();
                                    }
                                }
                            }
                            return;
                        }
                    }
                }

                // If we got here, we're not hovering over a valid character
                UpdateHoveredCharacter(null);

                // Handle clicking empty space to deselect
                if (Input.GetMouseButtonDown(0))
                {
                    DeselectCharacter();
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Error in HandleCharacterSelection: {e.Message}");
                UpdateHoveredCharacter(null);
            }
        }
        
        private void HandleSelectedCharacterInput()
        {
            if (selectedCharacter == null) return;
            
            // Right-click to cancel selection
            if (Input.GetMouseButtonDown(1))
            {
                DeselectCharacter();
                return;
            }
            
            // Handle ability selection (1-9 keys)
            var availableActions = selectedCharacter.GetAvailableActions();
            if (availableActions == null || availableActions.Count == 0) return;
            
            for (int i = 0; i < Mathf.Min(9, availableActions.Count); i++)
            {
                if (Input.GetKeyDown(KeyCode.Alpha1 + i))
                {
                    selectedAction = availableActions[i];
                    Debug.Log($"Selected action: {selectedAction.ActionName}");
                    
                    // If this is a self-targeting ability, use it immediately
                    if (selectedAction.CanTargetSelf && selectedAction.IsValidTarget(selectedCharacter, selectedCharacter))
                    {
                        battleManager.UseAbility(selectedCharacter, selectedCharacter, selectedAction);
                        DeselectCharacter();
                    }
                    break;
                }
            }
        }
        
        private void UpdateHoveredCharacter(ICombatParticipant newHovered)
        {
            try
            {
                // Skip if the hovered character hasn't changed
                if (hoveredCharacter == newHovered) return;
                
                // Reset previous hovered character if it's not the selected one
                if (hoveredCharacter != null && (selectedCharacter == null || hoveredCharacter != selectedCharacter))
                {
                    var visual = (hoveredCharacter as MonoBehaviour)?.GetComponentInChildren<BaseCharacter>();
                    if (visual != null)
                    {
                        visual.ResetHighlight();
                    }
                }
                
                // Update hovered character
                hoveredCharacter = newHovered;
                
                // Highlight new hovered character if it's not the selected one and is alive
                if (hoveredCharacter != null && hoveredCharacter.IsAlive && 
                    (selectedCharacter == null || hoveredCharacter != selectedCharacter))
                {
                    var visual = (hoveredCharacter as MonoBehaviour)?.GetComponentInChildren<BaseCharacter>();
                    if (visual != null)
                    {
                        visual.Highlight(hoverColor);
                    }
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Error updating hovered character: {e.Message}");
                hoveredCharacter = null;
            }
        }
        
        private void SelectCharacter(ICombatParticipant character)
        {
            if (character == null) return;
            
            // Don't select if the character is already selected, not player-controlled, or not alive
            if (selectedCharacter == character || !character.IsPlayerControlled || !character.IsAlive) 
            {
                Debug.Log($"Cannot select character: Already selected={selectedCharacter == character}, " +
                         $"PlayerControlled={character.IsPlayerControlled}, Alive={character.IsAlive}");
                return;
            }
            
            // Deselect current character
            DeselectCharacter();
            
            try
            {
                // Select new character
                selectedCharacter = character;
                
                // Get the visual component for highlighting
                var visual = (selectedCharacter as MonoBehaviour)?.GetComponentInChildren<BaseCharacter>();
                if (visual != null)
                {
                    visual.Highlight(selectedColor);
                }
                
                selectedAction = null;
                
                // Notify UI or other systems about the selection
                battleManager.OnCharacterSelected(character);
                
                Debug.Log($"Selected character: {character.ParticipantName}");
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Error selecting character: {e.Message}");
                selectedCharacter = null;
                selectedAction = null;
            }
        }
        
        private void DeselectCharacter()
        {
            if (selectedCharacter != null)
            {
                // Reset highlight on the visual component
                var visual = (selectedCharacter as MonoBehaviour)?.GetComponentInChildren<BaseCharacter>();
                if (visual != null)
                {
                    visual.ResetHighlight();
                }
                
                // Notify UI or other systems about the deselection
                battleManager.OnCharacterDeselected(selectedCharacter);
                
                // Clear references
                selectedCharacter = null;
                selectedAction = null;
                
                Debug.Log("Deselected character");
            }
        }
        
        private void OnDisable()
        {
            // Clean up when disabled
            if (hoveredCharacter != null && (selectedCharacter == null || hoveredCharacter != selectedCharacter))
            {
                var hoverVisual = (hoveredCharacter as MonoBehaviour)?.GetComponentInChildren<BaseCharacter>();
                if (hoverVisual != null)
                {
                    hoverVisual.ResetHighlight();
                }
            }
            
            if (selectedCharacter != null)
            {
                var selectVisual = (selectedCharacter as MonoBehaviour)?.GetComponentInChildren<BaseCharacter>();
                if (selectVisual != null)
                {
                    selectVisual.ResetHighlight();
                }
                
                // Notify about deselection
                battleManager.OnCharacterDeselected(selectedCharacter);
            }
            
            // Clear all references
            hoveredCharacter = null;
            selectedCharacter = null;
            selectedAction = null;
        }
    }
}
