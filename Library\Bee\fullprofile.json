{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 7132, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 7132, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 7132, "tid": 104, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 7132, "tid": 104, "ts": 1750351523081635, "dur": 1534, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 7132, "tid": 104, "ts": 1750351523083249, "dur": 16, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 7132, "tid": 107374182400, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500641150, "dur": 15474, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500656626, "dur": 22423466, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500656636, "dur": 40, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500656681, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500656684, "dur": 45334, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500702029, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500702034, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500702091, "dur": 55, "ph": "X", "name": "ProcessMessages 41", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500702147, "dur": 2167, "ph": "X", "name": "ReadAsync 41", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500704326, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500704330, "dur": 119, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500704453, "dur": 5, "ph": "X", "name": "ProcessMessages 2810", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500704460, "dur": 60, "ph": "X", "name": "ReadAsync 2810", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500704523, "dur": 1, "ph": "X", "name": "ProcessMessages 989", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500704527, "dur": 48, "ph": "X", "name": "ReadAsync 989", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500704579, "dur": 2, "ph": "X", "name": "ProcessMessages 632", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500704582, "dur": 36, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500704621, "dur": 1, "ph": "X", "name": "ProcessMessages 171", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500704623, "dur": 93, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500704721, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500704775, "dur": 1, "ph": "X", "name": "ProcessMessages 622", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500704778, "dur": 67, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500704849, "dur": 49, "ph": "X", "name": "ReadAsync 238", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500704901, "dur": 2, "ph": "X", "name": "ProcessMessages 910", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500704903, "dur": 44, "ph": "X", "name": "ReadAsync 910", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500704951, "dur": 69, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500705024, "dur": 1, "ph": "X", "name": "ProcessMessages 924", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500705026, "dur": 39, "ph": "X", "name": "ReadAsync 924", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500705069, "dur": 1, "ph": "X", "name": "ProcessMessages 730", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500705071, "dur": 40, "ph": "X", "name": "ReadAsync 730", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500705113, "dur": 1, "ph": "X", "name": "ProcessMessages 609", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500705115, "dur": 37, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500705154, "dur": 1, "ph": "X", "name": "ProcessMessages 696", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500705156, "dur": 48, "ph": "X", "name": "ReadAsync 696", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500705207, "dur": 2, "ph": "X", "name": "ProcessMessages 59", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500705210, "dur": 373, "ph": "X", "name": "ReadAsync 59", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500705587, "dur": 4, "ph": "X", "name": "ProcessMessages 5080", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500705592, "dur": 128, "ph": "X", "name": "ReadAsync 5080", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500705723, "dur": 3, "ph": "X", "name": "ProcessMessages 2380", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500705727, "dur": 139, "ph": "X", "name": "ReadAsync 2380", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500705868, "dur": 2, "ph": "X", "name": "ProcessMessages 2507", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500705871, "dur": 134, "ph": "X", "name": "ReadAsync 2507", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500706007, "dur": 2, "ph": "X", "name": "ProcessMessages 1917", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500706010, "dur": 140, "ph": "X", "name": "ReadAsync 1917", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500706152, "dur": 2, "ph": "X", "name": "ProcessMessages 2115", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500706155, "dur": 145, "ph": "X", "name": "ReadAsync 2115", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500706302, "dur": 2, "ph": "X", "name": "ProcessMessages 2701", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500706305, "dur": 129, "ph": "X", "name": "ReadAsync 2701", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500706436, "dur": 2, "ph": "X", "name": "ProcessMessages 3029", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500706439, "dur": 136, "ph": "X", "name": "ReadAsync 3029", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500706577, "dur": 2, "ph": "X", "name": "ProcessMessages 1949", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500706580, "dur": 118, "ph": "X", "name": "ReadAsync 1949", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500706699, "dur": 2, "ph": "X", "name": "ProcessMessages 2526", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500706702, "dur": 69, "ph": "X", "name": "ReadAsync 2526", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500706774, "dur": 2, "ph": "X", "name": "ProcessMessages 2616", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500706777, "dur": 38, "ph": "X", "name": "ReadAsync 2616", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500706818, "dur": 108, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500706929, "dur": 40, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500706970, "dur": 1, "ph": "X", "name": "ProcessMessages 1650", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500706972, "dur": 110, "ph": "X", "name": "ReadAsync 1650", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500707084, "dur": 1, "ph": "X", "name": "ProcessMessages 831", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500707086, "dur": 135, "ph": "X", "name": "ReadAsync 831", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500707223, "dur": 2, "ph": "X", "name": "ProcessMessages 2558", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500707225, "dur": 139, "ph": "X", "name": "ReadAsync 2558", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500707366, "dur": 1, "ph": "X", "name": "ProcessMessages 2426", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500707369, "dur": 123, "ph": "X", "name": "ReadAsync 2426", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500707494, "dur": 1, "ph": "X", "name": "ProcessMessages 2801", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500707496, "dur": 133, "ph": "X", "name": "ReadAsync 2801", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500707631, "dur": 2, "ph": "X", "name": "ProcessMessages 2775", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500707633, "dur": 124, "ph": "X", "name": "ReadAsync 2775", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500707759, "dur": 1, "ph": "X", "name": "ProcessMessages 2488", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500707761, "dur": 47, "ph": "X", "name": "ReadAsync 2488", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500707810, "dur": 1, "ph": "X", "name": "ProcessMessages 2307", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500707812, "dur": 97, "ph": "X", "name": "ReadAsync 2307", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500707911, "dur": 90, "ph": "X", "name": "ReadAsync 689", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500708003, "dur": 2, "ph": "X", "name": "ProcessMessages 2949", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500708006, "dur": 76, "ph": "X", "name": "ReadAsync 2949", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500708084, "dur": 1, "ph": "X", "name": "ProcessMessages 1792", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500708086, "dur": 88, "ph": "X", "name": "ReadAsync 1792", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500708176, "dur": 1, "ph": "X", "name": "ProcessMessages 984", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500708179, "dur": 87, "ph": "X", "name": "ReadAsync 984", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500708267, "dur": 2, "ph": "X", "name": "ProcessMessages 2344", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500708270, "dur": 94, "ph": "X", "name": "ReadAsync 2344", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500708366, "dur": 1, "ph": "X", "name": "ProcessMessages 1859", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500708368, "dur": 93, "ph": "X", "name": "ReadAsync 1859", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500708463, "dur": 1, "ph": "X", "name": "ProcessMessages 2321", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500708465, "dur": 94, "ph": "X", "name": "ReadAsync 2321", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500708561, "dur": 1, "ph": "X", "name": "ProcessMessages 1846", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500708563, "dur": 101, "ph": "X", "name": "ReadAsync 1846", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500708666, "dur": 1, "ph": "X", "name": "ProcessMessages 1757", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500708668, "dur": 98, "ph": "X", "name": "ReadAsync 1757", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500708768, "dur": 1, "ph": "X", "name": "ProcessMessages 1497", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500708770, "dur": 100, "ph": "X", "name": "ReadAsync 1497", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500708871, "dur": 1, "ph": "X", "name": "ProcessMessages 2299", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500708873, "dur": 282, "ph": "X", "name": "ReadAsync 2299", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500709182, "dur": 1, "ph": "X", "name": "ProcessMessages 1801", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500709209, "dur": 786, "ph": "X", "name": "ReadAsync 1801", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500709997, "dur": 6, "ph": "X", "name": "ProcessMessages 9631", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500710004, "dur": 108, "ph": "X", "name": "ReadAsync 9631", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500710118, "dur": 3, "ph": "X", "name": "ProcessMessages 2184", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500710123, "dur": 32, "ph": "X", "name": "ReadAsync 2184", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500710156, "dur": 1, "ph": "X", "name": "ProcessMessages 626", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500710158, "dur": 85, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500710247, "dur": 1, "ph": "X", "name": "ProcessMessages 293", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500710250, "dur": 100, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500710352, "dur": 2, "ph": "X", "name": "ProcessMessages 2018", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500710355, "dur": 425, "ph": "X", "name": "ReadAsync 2018", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500710785, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500710823, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500710826, "dur": 66, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500710896, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500710922, "dur": 40, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500710966, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500710989, "dur": 56, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500711049, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500711064, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500711065, "dur": 46, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500711115, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500711138, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500711160, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500711189, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500711209, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500711231, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500711251, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500711276, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500711297, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500711318, "dur": 17, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500711338, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500711357, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500711387, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500711427, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500711463, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500711502, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500711505, "dur": 34, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500711542, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500711544, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500711570, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500711572, "dur": 43, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500711619, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500711650, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500711652, "dur": 22, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500711677, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500711680, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500711707, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500711737, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500711738, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500711770, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500711801, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500711803, "dur": 30, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500711835, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500711837, "dur": 23, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500711864, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500711891, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500711919, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500711950, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500711952, "dur": 22, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500711978, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500712005, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500712031, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500712033, "dur": 23, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500712059, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500712093, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500712094, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500712120, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500712122, "dur": 23, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500712148, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500712149, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500712176, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500712206, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500712232, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500712234, "dur": 28, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500712266, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500712291, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500712293, "dur": 50, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500712347, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500712349, "dur": 102, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500712455, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500712457, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500712489, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500712491, "dur": 23, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500712518, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500712543, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500712545, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500712571, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500712593, "dur": 33, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500712629, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500712654, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500712658, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500712684, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500712686, "dur": 25, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500712714, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500712742, "dur": 49, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500712794, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500712796, "dur": 36, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500712837, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500712865, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500712867, "dur": 24, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500712895, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500712925, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500712955, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500712980, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500712982, "dur": 22, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500713008, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500713033, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500713035, "dur": 23, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500713060, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500713061, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500713090, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500713116, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500713118, "dur": 27, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500713147, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500713149, "dur": 22, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500713173, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500713175, "dur": 28, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500713207, "dur": 22, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500713231, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500713233, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500713258, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500713260, "dur": 23, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500713287, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500713313, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500713315, "dur": 22, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500713341, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500713366, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500713390, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500713392, "dur": 24, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500713421, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500713422, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500713451, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500713452, "dur": 21, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500713476, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500713478, "dur": 22, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500713504, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500713533, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500713560, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500713584, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500713607, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500713609, "dur": 35, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500713647, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500713649, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500713676, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500713678, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500713705, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500713737, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500713762, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500713763, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500713793, "dur": 44, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500713841, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500713897, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500713900, "dur": 38, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500713941, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500713943, "dur": 65, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500714010, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500714012, "dur": 22, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500714038, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500714064, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500714066, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500714091, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500714116, "dur": 100, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500714220, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500714251, "dur": 44, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500714299, "dur": 64, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500714369, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500714372, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500714404, "dur": 41, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500714449, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500714451, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500714500, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500714503, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500714533, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500714562, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500714589, "dur": 37, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500714630, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500714664, "dur": 43, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500714711, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500714753, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500714758, "dur": 36, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500714796, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500714798, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500714825, "dur": 137, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500714966, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500714968, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500714997, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500714999, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500715025, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500715058, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500715086, "dur": 41, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500715131, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500715163, "dur": 3, "ph": "X", "name": "ProcessMessages 40", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500715168, "dur": 22, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500715194, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500715218, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500715242, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500715275, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500715305, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500715308, "dur": 40, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500715351, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500715378, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500715379, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500715407, "dur": 76, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500715487, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500715514, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500715546, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500715573, "dur": 198, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500715776, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500715831, "dur": 502, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500716338, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500716340, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500716377, "dur": 102, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500716482, "dur": 4022, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500720513, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500720518, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500720550, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500720553, "dur": 40, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500720600, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500720603, "dur": 510, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500721116, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500721118, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500721157, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500721160, "dur": 2426, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500723593, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500723596, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500723634, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500723636, "dur": 31, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500723670, "dur": 36, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500723709, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500723742, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500723744, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500723772, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500723808, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500723856, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500723858, "dur": 38, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500723899, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500723921, "dur": 479, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500724405, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500724436, "dur": 60, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500724502, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500724528, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500724559, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500724588, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500724590, "dur": 172, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500724766, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500724791, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500724813, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500724835, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500724857, "dur": 158, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500725020, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500725043, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500725045, "dur": 19, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500725067, "dur": 169, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500725239, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500725262, "dur": 53, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500725318, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500725344, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500725374, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500725402, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500725420, "dur": 182, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500725630, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500725708, "dur": 49, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500725760, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500725762, "dur": 21, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500725787, "dur": 26, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500725816, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500725838, "dur": 29, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500725872, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500725874, "dur": 230, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500726108, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500726109, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500726141, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500726143, "dur": 223, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500726370, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500726394, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500726420, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500726444, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500726446, "dur": 151, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500726600, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500726638, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500726640, "dur": 240, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500726884, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500726902, "dur": 76, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500726983, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500726987, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500727040, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500727042, "dur": 135, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500727183, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351500727227, "dur": 21817218, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351522544468, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351522544475, "dur": 79, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351522544558, "dur": 30, "ph": "X", "name": "ProcessMessages 383", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351522544590, "dur": 4402, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351522549011, "dur": 11, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351522549024, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351522549056, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351522549060, "dur": 2678, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351522551747, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351522551750, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351522551800, "dur": 20, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351522551821, "dur": 5001, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351522556832, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351522556836, "dur": 216, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351522557245, "dur": 3, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351522557249, "dur": 21956, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351522579216, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351522579220, "dur": 160, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351522579391, "dur": 6, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351522579399, "dur": 831, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351522580237, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351522580240, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351522580272, "dur": 28, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351522580302, "dur": 254997, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351522835308, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351522835312, "dur": 64, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351522835380, "dur": 30, "ph": "X", "name": "ProcessMessages 375", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351522835412, "dur": 6614, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351522842038, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351522842044, "dur": 66, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351522842116, "dur": 5, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351522842122, "dur": 130, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351522842257, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351522842259, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351522842305, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351522842309, "dur": 177, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351522842490, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351522842493, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351522842531, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351522842533, "dur": 905, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351522843446, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351522843449, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351522843495, "dur": 24, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351522843520, "dur": 11461, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351522854995, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351522855000, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351522855038, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351522855043, "dur": 1154, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351522856208, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351522856212, "dur": 137, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351522856356, "dur": 24, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351522856382, "dur": 21881, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351522878275, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351522878279, "dur": 66, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351522878350, "dur": 33, "ph": "X", "name": "ProcessMessages 1193", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351522878385, "dur": 4526, "ph": "X", "name": "ReadAsync 1193", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351522882921, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351522882925, "dur": 84, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351522883017, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351522883022, "dur": 776, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351522883803, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351522883806, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351522883848, "dur": 23, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351522883872, "dur": 99853, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351522983738, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351522983743, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351522983789, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351522983795, "dur": 2333, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351522986142, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351522986148, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351522986180, "dur": 21, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351522986203, "dur": 7013, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351522993227, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351522993233, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351522993282, "dur": 28, "ph": "X", "name": "ProcessMessages 605", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351522993312, "dur": 59964, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351523053286, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351523053289, "dur": 125, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351523053419, "dur": 24, "ph": "X", "name": "ProcessMessages 764", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351523053444, "dur": 20244, "ph": "X", "name": "ReadAsync 764", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351523073697, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351523073701, "dur": 162, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351523073866, "dur": 2, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 7132, "tid": 107374182400, "ts": 1750351523074019, "dur": 6060, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 7132, "tid": 104, "ts": 1750351523083268, "dur": 11900, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 7132, "tid": 103079215104, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 7132, "tid": 103079215104, "ts": 1750351500640964, "dur": 16, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 7132, "tid": 103079215104, "ts": 1750351500640980, "dur": 15634, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 7132, "tid": 103079215104, "ts": 1750351500656616, "dur": 59, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 7132, "tid": 104, "ts": 1750351523095170, "dur": 6, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 7132, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 7132, "tid": 1, "ts": 1750351497272714, "dur": 3581, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 7132, "tid": 1, "ts": 1750351497276300, "dur": 25484, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 7132, "tid": 1, "ts": 1750351497301786, "dur": 31157, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 7132, "tid": 104, "ts": 1750351523095178, "dur": 34, "ph": "X", "name": "", "args": {}}, {"pid": 7132, "tid": 98784247808, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497272431, "dur": 26006, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497298447, "dur": 48022, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497298482, "dur": 260, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497298748, "dur": 10, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497298774, "dur": 10207, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497308994, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497308998, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497309038, "dur": 2947, "ph": "X", "name": "ProcessMessages 41", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497311997, "dur": 18205, "ph": "X", "name": "ReadAsync 41", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497330210, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497330214, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497330248, "dur": 1, "ph": "X", "name": "ProcessMessages 632", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497330253, "dur": 42, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497330301, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497330305, "dur": 48, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497330356, "dur": 1, "ph": "X", "name": "ProcessMessages 341", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497330358, "dur": 31, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497330391, "dur": 1, "ph": "X", "name": "ProcessMessages 325", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497330393, "dur": 46, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497330441, "dur": 1, "ph": "X", "name": "ProcessMessages 600", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497330443, "dur": 21, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497330465, "dur": 1, "ph": "X", "name": "ProcessMessages 606", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497330467, "dur": 28, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497330498, "dur": 24, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497330528, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497330570, "dur": 1, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497330572, "dur": 36, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497330611, "dur": 1, "ph": "X", "name": "ProcessMessages 360", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497330613, "dur": 31, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497330647, "dur": 31, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497330680, "dur": 1, "ph": "X", "name": "ProcessMessages 289", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497330682, "dur": 28, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497330712, "dur": 1, "ph": "X", "name": "ProcessMessages 199", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497330714, "dur": 74, "ph": "X", "name": "ReadAsync 199", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497330790, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497330793, "dur": 349, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497331160, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497331163, "dur": 112, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497331278, "dur": 5, "ph": "X", "name": "ProcessMessages 5068", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497331286, "dur": 45, "ph": "X", "name": "ReadAsync 5068", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497331336, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497331393, "dur": 1, "ph": "X", "name": "ProcessMessages 284", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497331396, "dur": 62, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497331462, "dur": 1, "ph": "X", "name": "ProcessMessages 594", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497331465, "dur": 58, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497331527, "dur": 2, "ph": "X", "name": "ProcessMessages 610", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497331530, "dur": 50, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497331582, "dur": 1, "ph": "X", "name": "ProcessMessages 622", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497331584, "dur": 47, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497331635, "dur": 173, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497331810, "dur": 2, "ph": "X", "name": "ProcessMessages 1464", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497331814, "dur": 48, "ph": "X", "name": "ReadAsync 1464", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497331863, "dur": 1, "ph": "X", "name": "ProcessMessages 551", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497331865, "dur": 33, "ph": "X", "name": "ReadAsync 551", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497331900, "dur": 1, "ph": "X", "name": "ProcessMessages 290", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497331902, "dur": 46, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497331950, "dur": 1, "ph": "X", "name": "ProcessMessages 353", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497331952, "dur": 53, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497332008, "dur": 1, "ph": "X", "name": "ProcessMessages 419", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497332010, "dur": 135, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497332149, "dur": 1, "ph": "X", "name": "ProcessMessages 465", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497332151, "dur": 68, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497332225, "dur": 3, "ph": "X", "name": "ProcessMessages 2092", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497332229, "dur": 121, "ph": "X", "name": "ReadAsync 2092", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497332354, "dur": 1, "ph": "X", "name": "ProcessMessages 276", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497332356, "dur": 71, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497332430, "dur": 2, "ph": "X", "name": "ProcessMessages 1524", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497332433, "dur": 69, "ph": "X", "name": "ReadAsync 1524", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497332508, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497332510, "dur": 39, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497332552, "dur": 1, "ph": "X", "name": "ProcessMessages 248", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497332554, "dur": 104, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497332664, "dur": 1, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497332667, "dur": 72, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497332742, "dur": 2, "ph": "X", "name": "ProcessMessages 1935", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497332745, "dur": 44, "ph": "X", "name": "ReadAsync 1935", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497332793, "dur": 2, "ph": "X", "name": "ProcessMessages 455", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497332796, "dur": 85, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497332885, "dur": 1, "ph": "X", "name": "ProcessMessages 614", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497332888, "dur": 67, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497332959, "dur": 2, "ph": "X", "name": "ProcessMessages 1473", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497332962, "dur": 49, "ph": "X", "name": "ReadAsync 1473", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497333015, "dur": 1, "ph": "X", "name": "ProcessMessages 177", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497333018, "dur": 55, "ph": "X", "name": "ReadAsync 177", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497333076, "dur": 1, "ph": "X", "name": "ProcessMessages 840", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497333078, "dur": 51, "ph": "X", "name": "ReadAsync 840", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497333131, "dur": 1, "ph": "X", "name": "ProcessMessages 678", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497333134, "dur": 61, "ph": "X", "name": "ReadAsync 678", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497333199, "dur": 2, "ph": "X", "name": "ProcessMessages 695", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497333204, "dur": 68, "ph": "X", "name": "ReadAsync 695", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497333276, "dur": 2, "ph": "X", "name": "ProcessMessages 1045", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497333279, "dur": 92, "ph": "X", "name": "ReadAsync 1045", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497333375, "dur": 2, "ph": "X", "name": "ProcessMessages 632", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497333379, "dur": 62, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497333446, "dur": 2, "ph": "X", "name": "ProcessMessages 1202", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497333450, "dur": 83, "ph": "X", "name": "ReadAsync 1202", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497333537, "dur": 2, "ph": "X", "name": "ProcessMessages 1017", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497333540, "dur": 57, "ph": "X", "name": "ReadAsync 1017", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497333600, "dur": 2, "ph": "X", "name": "ProcessMessages 477", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497333603, "dur": 53, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497333659, "dur": 1, "ph": "X", "name": "ProcessMessages 875", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497333661, "dur": 152, "ph": "X", "name": "ReadAsync 875", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497333818, "dur": 1, "ph": "X", "name": "ProcessMessages 1", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497333822, "dur": 152, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497333978, "dur": 3, "ph": "X", "name": "ProcessMessages 2502", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497333982, "dur": 71, "ph": "X", "name": "ReadAsync 2502", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497334057, "dur": 2, "ph": "X", "name": "ProcessMessages 1431", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497334060, "dur": 201, "ph": "X", "name": "ReadAsync 1431", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497334265, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497334267, "dur": 149, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497334420, "dur": 12, "ph": "X", "name": "ProcessMessages 2286", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497334433, "dur": 131, "ph": "X", "name": "ReadAsync 2286", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497334568, "dur": 1, "ph": "X", "name": "ProcessMessages 747", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497334570, "dur": 128, "ph": "X", "name": "ReadAsync 747", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497334702, "dur": 2, "ph": "X", "name": "ProcessMessages 1266", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497334705, "dur": 164, "ph": "X", "name": "ReadAsync 1266", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497334872, "dur": 3, "ph": "X", "name": "ProcessMessages 2346", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497334876, "dur": 184, "ph": "X", "name": "ReadAsync 2346", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497335064, "dur": 1, "ph": "X", "name": "ProcessMessages 813", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497335067, "dur": 159, "ph": "X", "name": "ReadAsync 813", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497335229, "dur": 2, "ph": "X", "name": "ProcessMessages 1265", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497335232, "dur": 135, "ph": "X", "name": "ReadAsync 1265", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497335371, "dur": 2, "ph": "X", "name": "ProcessMessages 1417", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497335374, "dur": 153, "ph": "X", "name": "ReadAsync 1417", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497335531, "dur": 2, "ph": "X", "name": "ProcessMessages 1814", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497335535, "dur": 63, "ph": "X", "name": "ReadAsync 1814", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497335601, "dur": 1, "ph": "X", "name": "ProcessMessages 955", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497335604, "dur": 114, "ph": "X", "name": "ReadAsync 955", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497335722, "dur": 1, "ph": "X", "name": "ProcessMessages 286", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497335724, "dur": 64, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497335791, "dur": 2, "ph": "X", "name": "ProcessMessages 1627", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497335795, "dur": 37, "ph": "X", "name": "ReadAsync 1627", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497335835, "dur": 1, "ph": "X", "name": "ProcessMessages 610", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497335837, "dur": 42, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497335881, "dur": 1, "ph": "X", "name": "ProcessMessages 310", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497335883, "dur": 45, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497335931, "dur": 1, "ph": "X", "name": "ProcessMessages 725", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497335934, "dur": 107, "ph": "X", "name": "ReadAsync 725", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497336044, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497336046, "dur": 155, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497336205, "dur": 14, "ph": "X", "name": "ProcessMessages 2183", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497336222, "dur": 128, "ph": "X", "name": "ReadAsync 2183", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497336355, "dur": 2, "ph": "X", "name": "ProcessMessages 1388", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497336358, "dur": 172, "ph": "X", "name": "ReadAsync 1388", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497336534, "dur": 3, "ph": "X", "name": "ProcessMessages 2707", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497336538, "dur": 164, "ph": "X", "name": "ReadAsync 2707", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497336706, "dur": 2, "ph": "X", "name": "ProcessMessages 1265", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497336709, "dur": 211, "ph": "X", "name": "ReadAsync 1265", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497336924, "dur": 4, "ph": "X", "name": "ProcessMessages 2818", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497336929, "dur": 161, "ph": "X", "name": "ReadAsync 2818", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497337095, "dur": 2, "ph": "X", "name": "ProcessMessages 2316", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497337099, "dur": 86, "ph": "X", "name": "ReadAsync 2316", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497337187, "dur": 2, "ph": "X", "name": "ProcessMessages 2001", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497337190, "dur": 116, "ph": "X", "name": "ReadAsync 2001", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497337311, "dur": 152, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497337467, "dur": 2, "ph": "X", "name": "ProcessMessages 2297", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497337470, "dur": 127, "ph": "X", "name": "ReadAsync 2297", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497337601, "dur": 1, "ph": "X", "name": "ProcessMessages 1046", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497337603, "dur": 153, "ph": "X", "name": "ReadAsync 1046", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497337760, "dur": 3, "ph": "X", "name": "ProcessMessages 2203", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497337764, "dur": 164, "ph": "X", "name": "ReadAsync 2203", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497337932, "dur": 2, "ph": "X", "name": "ProcessMessages 1924", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497337936, "dur": 157, "ph": "X", "name": "ReadAsync 1924", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497338096, "dur": 2, "ph": "X", "name": "ProcessMessages 1991", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497338099, "dur": 75, "ph": "X", "name": "ReadAsync 1991", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497338179, "dur": 2, "ph": "X", "name": "ProcessMessages 1187", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497338182, "dur": 149, "ph": "X", "name": "ReadAsync 1187", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497338334, "dur": 1, "ph": "X", "name": "ProcessMessages 1204", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497338337, "dur": 139, "ph": "X", "name": "ReadAsync 1204", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497338479, "dur": 2, "ph": "X", "name": "ProcessMessages 1168", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497338482, "dur": 133, "ph": "X", "name": "ReadAsync 1168", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497338619, "dur": 1, "ph": "X", "name": "ProcessMessages 1168", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497338622, "dur": 213, "ph": "X", "name": "ReadAsync 1168", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497338843, "dur": 3, "ph": "X", "name": "ProcessMessages 1243", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497338848, "dur": 193, "ph": "X", "name": "ReadAsync 1243", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497339044, "dur": 3, "ph": "X", "name": "ProcessMessages 3162", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497339048, "dur": 128, "ph": "X", "name": "ReadAsync 3162", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497339180, "dur": 2, "ph": "X", "name": "ProcessMessages 1273", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497339182, "dur": 102, "ph": "X", "name": "ReadAsync 1273", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497339290, "dur": 3, "ph": "X", "name": "ProcessMessages 2529", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497339295, "dur": 49, "ph": "X", "name": "ReadAsync 2529", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497339346, "dur": 2, "ph": "X", "name": "ProcessMessages 215", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497339349, "dur": 46, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497339398, "dur": 1, "ph": "X", "name": "ProcessMessages 438", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497339401, "dur": 134, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497339539, "dur": 1, "ph": "X", "name": "ProcessMessages 641", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497339541, "dur": 80, "ph": "X", "name": "ReadAsync 641", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497339623, "dur": 1, "ph": "X", "name": "ProcessMessages 539", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497339626, "dur": 46, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497339674, "dur": 1, "ph": "X", "name": "ProcessMessages 550", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497339676, "dur": 100, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497339780, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497339783, "dur": 328, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497340115, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497340117, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497340151, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497340153, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497340192, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497340219, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497340221, "dur": 31, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497340256, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497340285, "dur": 23, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497340311, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497340334, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497340357, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497340379, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497340382, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497340413, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497340437, "dur": 35, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497340476, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497340501, "dur": 24, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497340528, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497340550, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497340551, "dur": 32, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497340587, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497340611, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497340634, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497340659, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497340689, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497340712, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497340714, "dur": 27, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497340743, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497340746, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497340769, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497340792, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497340814, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497340815, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497340853, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497340875, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497340876, "dur": 20, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497340900, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497340922, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497340946, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497340970, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497341002, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497341026, "dur": 77, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497341106, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497341108, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497341137, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497341139, "dur": 122, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497341266, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497341269, "dur": 129, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497341402, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497341404, "dur": 46, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497341454, "dur": 5, "ph": "X", "name": "ProcessMessages 25", "args": {}}, {"pid": 7132, "tid": 98784247808, "ts": 1750351497341460, "dur": 4947, "ph": "X", "name": "ReadAsync 25", "args": {}}, {"pid": 7132, "tid": 104, "ts": 1750351523095212, "dur": 1969, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 7132, "tid": 94489280512, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 7132, "tid": 94489280512, "ts": 1750351497271575, "dur": 61386, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 7132, "tid": 94489280512, "ts": 1750351497332963, "dur": 1, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 7132, "tid": 94489280512, "ts": 1750351497332964, "dur": 46, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 7132, "tid": 104, "ts": 1750351523097183, "dur": 32, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 7132, "tid": 90194313216, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 7132, "tid": 90194313216, "ts": 1750351497262004, "dur": 84527, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 7132, "tid": 90194313216, "ts": 1750351497262981, "dur": 8129, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 7132, "tid": 90194313216, "ts": 1750351497346579, "dur": 3290771, "ph": "X", "name": "await ExecuteBuildProgram", "args": {}}, {"pid": 7132, "tid": 90194313216, "ts": 1750351500637578, "dur": 22442572, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 7132, "tid": 90194313216, "ts": 1750351500637866, "dur": 3053, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 7132, "tid": 90194313216, "ts": 1750351523080159, "dur": 241, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 7132, "tid": 90194313216, "ts": 1750351523080249, "dur": 97, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 7132, "tid": 90194313216, "ts": 1750351523080402, "dur": 1, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 7132, "tid": 104, "ts": 1750351523097217, "dur": 149, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1750351500656931, "dur": 47140, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750351500704077, "dur": 115, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750351500704273, "dur": 331, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750351500704822, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_916E5E16AB469CF0.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750351500705193, "dur": 78, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_31E341F8DD700DC8.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750351500705326, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_02EB6148B760FC69.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750351500705907, "dur": 74, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_42F7D41B93B94AC7.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750351500706905, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_361A65A56C957CE2.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750351500709704, "dur": 726, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Actions.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1750351500710517, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1750351500704657, "dur": 6075, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750351500710739, "dur": 22346997, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750351523057738, "dur": 213, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750351523074042, "dur": 56, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750351523074123, "dur": 2316, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1750351500705375, "dur": 5565, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750351500710940, "dur": 707, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_D437D5B52E940013.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750351500711911, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_F5F9D4CEA3FE42FE.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750351500712022, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_1FC85B3DDABA9491.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750351500712126, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_1A4CC6E2C0ED3512.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750351500712222, "dur": 554, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_1A4CC6E2C0ED3512.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750351500712777, "dur": 284, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_4B6EE49D6AF37933.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750351500713062, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750351500713171, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750351500713286, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750351500713341, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_AE7AE761007ACD6C.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750351500713434, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_A3EA7E1937C99F5B.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750351500713664, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750351500713865, "dur": 10030, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1750351500723896, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750351500724085, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750351500724264, "dur": 470, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1750351500724735, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750351500724885, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750351500725031, "dur": 622, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1750351500725654, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750351500725904, "dur": 426, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750351500726454, "dur": 383, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750351500726838, "dur": 593, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750351500727431, "dur": 21821626, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750351522549058, "dur": 293083, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750351522842225, "dur": 153, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\TacticalCombatSystem.Status.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1750351522842142, "dur": 237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/TacticalCombatSystem.Status.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1750351522842412, "dur": 1463, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/TacticalCombatSystem.Status.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1750351522843879, "dur": 153254, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750351522997136, "dur": 60643, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750351500705223, "dur": 5634, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750351500710867, "dur": 553, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_16F59DAED9B12573.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750351500711421, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750351500711914, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_C412A4EF09BA29D5.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750351500711997, "dur": 525, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750351500712536, "dur": 218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_CB9C52C6DA4AB85A.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750351500712755, "dur": 249, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750351500713011, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_609C16FA6C001C3C.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750351500713212, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_686B6CE4ABC74178.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750351500713638, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Interfaces.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1750351500713932, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Status.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750351500714220, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750351500714291, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750351500714541, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750351500714691, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750351500714753, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Abilities.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750351500714904, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750351500715251, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Actions.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750351500715442, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750351500715517, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750351500715868, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750351500716016, "dur": 842, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750351500716859, "dur": 1309, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750351500718168, "dur": 448, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750351500718616, "dur": 721, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750351500719337, "dur": 668, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750351500720005, "dur": 712, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750351500720718, "dur": 761, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750351500721479, "dur": 668, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750351500722147, "dur": 756, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750351500722904, "dur": 417, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750351500723321, "dur": 419, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750351500723740, "dur": 413, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750351500724188, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750351500724291, "dur": 608, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750351500724912, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750351500725044, "dur": 175, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750351500725291, "dur": 227, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750351500725518, "dur": 395, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750351500725913, "dur": 409, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750351500726322, "dur": 128, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750351500726451, "dur": 427, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750351500726878, "dur": 466, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750351500727348, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750351500727458, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750351500727632, "dur": 21821319, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750351522549025, "dur": 307, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\TacticalCombatSystem.Interfaces.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1750351522548953, "dur": 380, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/TacticalCombatSystem.Interfaces.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1750351522549440, "dur": 2724, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/TacticalCombatSystem.Interfaces.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1750351522552170, "dur": 289965, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750351522842528, "dur": 125, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\TacticalCombatSystem.Status.ref.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750351522842708, "dur": 125, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\TacticalCombatSystem.Characters.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1750351522842137, "dur": 697, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Characters.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1750351522842838, "dur": 69, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750351522843021, "dur": 150616, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Characters.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1750351522997122, "dur": 60609, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750351500705084, "dur": 5726, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750351500710837, "dur": 537, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_EF62AA7CD312F942.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750351500711440, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_C62338721BDB794D.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750351500712011, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_51174E3C056538C2.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750351500712749, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750351500712892, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_C7FDD92EE7195664.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750351500713226, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_E0FCC25CAA3CC2C8.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750351500713420, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750351500713483, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750351500713626, "dur": 108, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_997BF679851B602C.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750351500713744, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1750351500713815, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750351500714058, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750351500714197, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750351500715746, "dur": 1370, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Characters.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1750351500717118, "dur": 815, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750351500717938, "dur": 734, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750351500718673, "dur": 733, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750351500719407, "dur": 662, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750351500720070, "dur": 758, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750351500720828, "dur": 721, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750351500721549, "dur": 642, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750351500722192, "dur": 655, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750351500722848, "dur": 658, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750351500723506, "dur": 728, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750351500724234, "dur": 773, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750351500725007, "dur": 312, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750351500725320, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750351500725471, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750351500725691, "dur": 503, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1750351500726194, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750351500726366, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750351500726451, "dur": 394, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750351500726845, "dur": 576, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750351500727421, "dur": 21821656, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750351522549078, "dur": 293061, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750351522842198, "dur": 13145, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\TacticalCombatSystem.Status.dll"}}, {"pid": 12345, "tid": 3, "ts": 1750351522842140, "dur": 13206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/TacticalCombatSystem.Status.dll"}}, {"pid": 12345, "tid": 3, "ts": 1750351522855370, "dur": 1233, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/TacticalCombatSystem.Status.dll"}}, {"pid": 12345, "tid": 3, "ts": 1750351522856609, "dur": 140521, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750351522997131, "dur": 60568, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750351500705070, "dur": 5678, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750351500712460, "dur": 1612, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.2.0b2/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 4, "ts": 1750351500710755, "dur": 3318, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750351500714073, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1750351500714200, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750351500714310, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750351500714361, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1750351500714457, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750351500714566, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Characters.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750351500714766, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750351500714897, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750351500714961, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750351500715037, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750351500715158, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Camera.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1750351500715214, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Camera.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750351500715616, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750351500715808, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Battle.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750351500715996, "dur": 474, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750351500716470, "dur": 514, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750351500716985, "dur": 531, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750351500717516, "dur": 952, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750351500718469, "dur": 598, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750351500719067, "dur": 868, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750351500719946, "dur": 525, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750351500720471, "dur": 458, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750351500720967, "dur": 534, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750351500721502, "dur": 454, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750351500721956, "dur": 438, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750351500722394, "dur": 519, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750351500722914, "dur": 475, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750351500723499, "dur": 753, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750351500724252, "dur": 738, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750351500724990, "dur": 519, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750351500725509, "dur": 411, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750351500725921, "dur": 399, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750351500726320, "dur": 138, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750351500726458, "dur": 373, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750351500726831, "dur": 608, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750351500727439, "dur": 21821606, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750351522549047, "dur": 293109, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750351522842157, "dur": 154967, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750351522997125, "dur": 60604, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750351500705205, "dur": 5639, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750351500710849, "dur": 304, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_CE52468F2AF94D3E.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750351500711157, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750351500711458, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750351500711590, "dur": 324, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_606993901B0ABE27.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750351500711919, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750351500712060, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750351500712730, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_F2C1277DFF099C79.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750351500713751, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Interfaces.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750351500713922, "dur": 6874, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Interfaces.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1750351500721424, "dur": 112, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750351500721540, "dur": 66, "ph": "X", "name": "EmitNodeStart", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750351500723669, "dur": 21821131, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Interfaces.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1750351522549432, "dur": 7437, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\TacticalCombatSystem.Interfaces.ref.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750351522548911, "dur": 7964, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Status.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1750351522558496, "dur": 277189, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Status.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1750351522842493, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\TacticalCombatSystem.Status.ref.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750351522842133, "dur": 455, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1750351522842593, "dur": 53, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750351522842759, "dur": 210929, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1750351500705158, "dur": 5676, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750351500710843, "dur": 497, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_163A7D00BA7D065C.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750351500711341, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750351500711420, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_BF8B01E2682946F2.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750351500711597, "dur": 416, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_5E1525907236878A.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750351500712013, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750351500712076, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_33A63B48735421F9.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750351500712373, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_5592C7A2A0C2A571.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750351500712660, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_8E9AC1970AF2CEA5.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750351500712712, "dur": 244, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750351500712964, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_F5DF255C08D11A8C.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750351500713039, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750351500713590, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1750351500714220, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750351500714565, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750351500714664, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1750351500714715, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750351500714798, "dur": 220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Abilities.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1750351500715019, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750351500715152, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Abilities.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1750351500715211, "dur": 945, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750351500716157, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750351500716213, "dur": 119, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750351500716377, "dur": 1752, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750351500718129, "dur": 517, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750351500718647, "dur": 533, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750351500719180, "dur": 703, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750351500719883, "dur": 643, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750351500720527, "dur": 773, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750351500721301, "dur": 676, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750351500721978, "dur": 836, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750351500722814, "dur": 378, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750351500723242, "dur": 635, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750351500723877, "dur": 156, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750351500724037, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750351500724141, "dur": 160, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Mathematics.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750351500724301, "dur": 550, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750351500724854, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750351500724958, "dur": 549, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750351500725508, "dur": 506, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1750351500726015, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750351500726304, "dur": 157, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750351500726461, "dur": 365, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750351500726861, "dur": 486, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750351500727347, "dur": 192, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750351500727539, "dur": 21821377, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750351522548977, "dur": 30609, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\TacticalCombatSystem.Interfaces.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750351522548918, "dur": 30670, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/TacticalCombatSystem.Interfaces.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750351522579615, "dur": 1050, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/TacticalCombatSystem.Interfaces.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750351522580670, "dur": 261484, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750351522842154, "dur": 154986, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750351522997141, "dur": 60630, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750351500705291, "dur": 5576, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750351500710874, "dur": 461, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_9B5BF5B045BFFED8.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750351500711336, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750351500711507, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_0399CE90B9A515B4.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750351500711602, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750351500711741, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_81157A417001B5EA.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750351500711992, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_E0273E4BBA1C33C0.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750351500712187, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750351500712370, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_C8FB050F2C14EF55.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750351500712712, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750351500712867, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_747257F2B95C7621.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750351500713629, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1750351500713832, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750351500714039, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750351500714235, "dur": 6585, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1750351500720821, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750351500721022, "dur": 506, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750351500721529, "dur": 481, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750351500722010, "dur": 448, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750351500722458, "dur": 667, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750351500723125, "dur": 701, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750351500723827, "dur": 303, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750351500724133, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750351500724193, "dur": 829, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750351500725022, "dur": 275, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750351500725309, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750351500725441, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750351500725511, "dur": 574, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1750351500726086, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750351500726215, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750351500726315, "dur": 144, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750351500726459, "dur": 371, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750351500726886, "dur": 460, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750351500727349, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750351500727428, "dur": 21821639, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750351522549068, "dur": 293075, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750351522842144, "dur": 41026, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750351522883213, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\TacticalCombatSystem.Battle.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1750351522883172, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/TacticalCombatSystem.Battle.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1750351522883312, "dur": 924, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/TacticalCombatSystem.Battle.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1750351522884240, "dur": 112887, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750351522997129, "dur": 60569, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750351500705356, "dur": 5524, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750351500710889, "dur": 482, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_2A38684436712F05.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750351500711453, "dur": 282, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750351500711761, "dur": 198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_C33E9E8B76E14557.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750351500711959, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750351500712136, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_42F7D41B93B94AC7.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750351500712263, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_9283034F04690CF9.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 17503***********, "dur": 198, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750351500712653, "dur": 236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_02EB6148B760FC69.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750351500713048, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_1F322CB3825CC582.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750351500713219, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750351500713288, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_585E665D2A6D4152.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750351500713465, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750351500713661, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750351500713814, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750351500713876, "dur": 10018, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1750351500723896, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750351500724043, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750351500724100, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TestRunner.ref.dll_E55D0F7C63F01D9E.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750351500724169, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750351500724285, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750351500724351, "dur": 791, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1750351500725143, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750351500725220, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750351500725304, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750351500725468, "dur": 628, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1750351500726097, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750351500726272, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750351500726440, "dur": 322, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1750351500726829, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750351500726891, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750351500727001, "dur": 281, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1750351500727446, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750351500727625, "dur": 21821287, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750351522549700, "dur": 7161, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\TacticalCombatSystem.Interfaces.ref.dll"}}, {"pid": 12345, "tid": 8, "ts": 1750351522548915, "dur": 7955, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Battle.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1750351522557095, "dur": 80, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750351522558626, "dur": 320053, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Battle.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1750351522883186, "dur": 100896, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\TacticalCombatSystem.Battle.dll"}}, {"pid": 12345, "tid": 8, "ts": 1750351522883164, "dur": 100920, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/TacticalCombatSystem.Battle.dll"}}, {"pid": 12345, "tid": 8, "ts": 1750351522984110, "dur": 2446, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/TacticalCombatSystem.Battle.dll"}}, {"pid": 12345, "tid": 8, "ts": 1750351522986561, "dur": 10576, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750351522997138, "dur": 60614, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750351523078982, "dur": 1134, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 35942, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "netcorerun.dll"}}, {"pid": 35942, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-1"}}, {"pid": 35942, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 35942, "tid": 1, "ts": 1750351500419010, "dur": 198581, "ph": "X", "name": "BuildProgram", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1750351500420110, "dur": 46837, "ph": "X", "name": "BuildProgramContextConstructor", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1750351500581173, "dur": 5808, "ph": "X", "name": "OutputData.Write", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1750351500586985, "dur": 30593, "ph": "X", "name": "Backend.Write", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1750351500587903, "dur": 24150, "ph": "X", "name": "JsonToString", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1750351500624135, "dur": 1091, "ph": "X", "name": "", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1750351500623678, "dur": 1753, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1750351497293070, "dur": 62, "ph": "X", "name": "IPC_Client_InitializeAndConnectToParent", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750351497293155, "dur": 36080, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750351497329244, "dur": 284, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750351497329606, "dur": 334, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750351497330049, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_BFE30F898F3B693C.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750351497330106, "dur": 75, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_9F4733395C159688.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750351497330290, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_E90840FC7BA4DF8A.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750351497330423, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_416ADE92277E457E.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750351497330545, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_747257F2B95C7621.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750351497331044, "dur": 152, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_4FF2CB3C17FB8E73.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750351497331509, "dur": 118, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_9283034F04690CF9.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750351497331997, "dur": 104, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_F0A87365319D71DD.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750351497332201, "dur": 108, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_BD985A39D270C9B7.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750351497332508, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_9A1C02F8E98C469D.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750351497333094, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_CE52468F2AF94D3E.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750351497333294, "dur": 74, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_B701B6B92B8026DD.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750351497333441, "dur": 98, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_988D394CD4FBAB22.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750351497333659, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_58AFBFB335E63D9E.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750351497333822, "dur": 144, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UWP.Extensions.dll_7000EBAFC0872D26.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750351497334550, "dur": 101, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1750351497334914, "dur": 106, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Multiplayer.Center.Common.ref.dll_EE36537354EA42C8.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750351497335217, "dur": 109, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1750351497335387, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Cinemachine.ref.dll_77EC0F35C376A022.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750351497335718, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750351497335890, "dur": 98, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1750351497336201, "dur": 100, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750351497336547, "dur": 110, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/TacticalCombatSystem.Interfaces.dll"}}, {"pid": 12345, "tid": 0, "ts": 1750351497336770, "dur": 105, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Mathematics.dll"}}, {"pid": 12345, "tid": 0, "ts": 1750351497336946, "dur": 102, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Battle.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1750351497337156, "dur": 112, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Status.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1750351497337605, "dur": 107, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Center.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1750351497337776, "dur": 100, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Abilities.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1750351497338022, "dur": 96, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Characters.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1750351497338184, "dur": 104, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Splines.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1750351497338465, "dur": 106, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/TacticalCombatSystem.Actions.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1750351497338697, "dur": 126, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1750351497339043, "dur": 106, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1750351497339291, "dur": 118, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750351497329971, "dur": 9522, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750351497339503, "dur": 1315, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750351497340819, "dur": 285, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750351497341245, "dur": 1939, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1750351497330273, "dur": 9289, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750351497339578, "dur": 1234, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750351497330385, "dur": 9224, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750351497339620, "dur": 310, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_163A7D00BA7D065C.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750351497339931, "dur": 363, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750351497340301, "dur": 582, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_9A1C02F8E98C469D.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750351497330336, "dur": 9243, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750351497339608, "dur": 275, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_EF62AA7CD312F942.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750351497339924, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_F78CC16BE3410205.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750351497340425, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750351497340524, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_F9164AECFF7EBE63.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750351497340677, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750351497330499, "dur": 9146, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750351497339653, "dur": 294, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_16F59DAED9B12573.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750351497339948, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750351497340256, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_AC3B8AED076F4CEB.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750351497340363, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750351497340793, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750351497330614, "dur": 9073, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750351497339692, "dur": 255, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_2A38684436712F05.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750351497339948, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750351497340083, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_C62338721BDB794D.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750351497340312, "dur": 578, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_D910E96BA6DA4E34.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750351497340890, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750351497330426, "dur": 9205, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750351497339641, "dur": 336, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_CE52468F2AF94D3E.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750351497340229, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750351497340355, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_81157A417001B5EA.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750351497340561, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750351497340696, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_9A8E98434D7100DD.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750351497330564, "dur": 9095, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750351497339668, "dur": 315, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_9B5BF5B045BFFED8.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750351497339984, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750351497340072, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_9B5BF5B045BFFED8.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750351497340131, "dur": 227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_8F9DC40C34B6425C.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750351497330707, "dur": 9028, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750351497339747, "dur": 544, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_D437D5B52E940013.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750351497340292, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750351497345302, "dur": 487, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 7132, "tid": 104, "ts": 1750351523099839, "dur": 108, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend2.traceevents"}}, {"pid": 7132, "tid": 104, "ts": 1750351523100666, "dur": 38, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "buildprogram0.traceevents"}}, {"pid": 7132, "tid": 104, "ts": 1750351523101311, "dur": 99, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 7132, "tid": 104, "ts": 1750351523100047, "dur": 618, "ph": "X", "name": "backend2.traceevents", "args": {}}, {"pid": 7132, "tid": 104, "ts": 1750351523100814, "dur": 497, "ph": "X", "name": "buildprogram0.traceevents", "args": {}}, {"pid": 7132, "tid": 104, "ts": 1750351523101521, "dur": 377, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 7132, "tid": 104, "ts": 1750351523083182, "dur": 18920, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}