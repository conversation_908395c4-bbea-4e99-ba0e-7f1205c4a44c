{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 7132, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 7132, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 7132, "tid": 97, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 7132, "tid": 97, "ts": 1750350670796484, "dur": 48, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 7132, "tid": 97, "ts": 1750350670796594, "dur": 76, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 7132, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 7132, "tid": 1, "ts": 1750350669538355, "dur": 3057, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 7132, "tid": 1, "ts": 1750350669541418, "dur": 22865, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 7132, "tid": 1, "ts": 1750350669564285, "dur": 21659, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 7132, "tid": 97, "ts": 1750350670796673, "dur": 37, "ph": "X", "name": "", "args": {}}, {"pid": 7132, "tid": 85899345920, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669538217, "dur": 20111, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669558331, "dur": 1236492, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669558351, "dur": 93, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669558448, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669558461, "dur": 877, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669559399, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669559444, "dur": 73, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669559519, "dur": 83, "ph": "X", "name": "ProcessMessages 41", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669559604, "dur": 9512, "ph": "X", "name": "ReadAsync 41", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669569122, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669569126, "dur": 526, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669569657, "dur": 3, "ph": "X", "name": "ProcessMessages 1302", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669569661, "dur": 198, "ph": "X", "name": "ReadAsync 1302", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669569862, "dur": 2, "ph": "X", "name": "ProcessMessages 2351", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669569898, "dur": 46, "ph": "X", "name": "ReadAsync 2351", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669569946, "dur": 1, "ph": "X", "name": "ProcessMessages 949", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669569949, "dur": 99, "ph": "X", "name": "ReadAsync 949", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669570053, "dur": 187, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669570244, "dur": 3, "ph": "X", "name": "ProcessMessages 2624", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669570248, "dur": 177, "ph": "X", "name": "ReadAsync 2624", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669570429, "dur": 2, "ph": "X", "name": "ProcessMessages 1794", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669570432, "dur": 51, "ph": "X", "name": "ReadAsync 1794", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669570496, "dur": 1, "ph": "X", "name": "ProcessMessages 650", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669570498, "dur": 206, "ph": "X", "name": "ReadAsync 650", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669570719, "dur": 2, "ph": "X", "name": "ProcessMessages 1355", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669570722, "dur": 177, "ph": "X", "name": "ReadAsync 1355", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669570902, "dur": 2, "ph": "X", "name": "ProcessMessages 1922", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669570906, "dur": 182, "ph": "X", "name": "ReadAsync 1922", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669571092, "dur": 2, "ph": "X", "name": "ProcessMessages 1745", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669571095, "dur": 59, "ph": "X", "name": "ReadAsync 1745", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669571157, "dur": 1, "ph": "X", "name": "ProcessMessages 1066", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669571159, "dur": 53, "ph": "X", "name": "ReadAsync 1066", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669571216, "dur": 2, "ph": "X", "name": "ProcessMessages 760", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669571219, "dur": 177, "ph": "X", "name": "ReadAsync 760", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669571400, "dur": 2, "ph": "X", "name": "ProcessMessages 1220", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669571403, "dur": 190, "ph": "X", "name": "ReadAsync 1220", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669571596, "dur": 2, "ph": "X", "name": "ProcessMessages 1732", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669571599, "dur": 44, "ph": "X", "name": "ReadAsync 1732", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669571645, "dur": 1, "ph": "X", "name": "ProcessMessages 936", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669571647, "dur": 107, "ph": "X", "name": "ReadAsync 936", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669571758, "dur": 1, "ph": "X", "name": "ProcessMessages 302", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669571776, "dur": 86, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669571865, "dur": 2, "ph": "X", "name": "ProcessMessages 2081", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669571869, "dur": 139, "ph": "X", "name": "ReadAsync 2081", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669572012, "dur": 2, "ph": "X", "name": "ProcessMessages 1380", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669572145, "dur": 795, "ph": "X", "name": "ReadAsync 1380", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669572944, "dur": 4, "ph": "X", "name": "ProcessMessages 5046", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669572949, "dur": 142, "ph": "X", "name": "ReadAsync 5046", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669573102, "dur": 2, "ph": "X", "name": "ProcessMessages 1235", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669573105, "dur": 48, "ph": "X", "name": "ReadAsync 1235", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669573156, "dur": 1, "ph": "X", "name": "ProcessMessages 857", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669573159, "dur": 181, "ph": "X", "name": "ReadAsync 857", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669573344, "dur": 2, "ph": "X", "name": "ProcessMessages 965", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669573347, "dur": 52, "ph": "X", "name": "ReadAsync 965", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669573404, "dur": 53, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669573460, "dur": 1, "ph": "X", "name": "ProcessMessages 974", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669573462, "dur": 29, "ph": "X", "name": "ReadAsync 974", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669573494, "dur": 1, "ph": "X", "name": "ProcessMessages 316", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669573496, "dur": 47, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669573546, "dur": 1, "ph": "X", "name": "ProcessMessages 582", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669573548, "dur": 46, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669573596, "dur": 1, "ph": "X", "name": "ProcessMessages 558", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669573597, "dur": 40, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669573640, "dur": 1, "ph": "X", "name": "ProcessMessages 558", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669573642, "dur": 43, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669573687, "dur": 1, "ph": "X", "name": "ProcessMessages 474", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669573689, "dur": 56, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669573748, "dur": 1, "ph": "X", "name": "ProcessMessages 774", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669573751, "dur": 34, "ph": "X", "name": "ReadAsync 774", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669573787, "dur": 1, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669573789, "dur": 34, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669573825, "dur": 1, "ph": "X", "name": "ProcessMessages 483", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669573827, "dur": 31, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669573861, "dur": 1, "ph": "X", "name": "ProcessMessages 303", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669573862, "dur": 33, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669573899, "dur": 24, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669573926, "dur": 347, "ph": "X", "name": "ReadAsync 65", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669574277, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669574279, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669574322, "dur": 1, "ph": "X", "name": "ProcessMessages 296", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669574323, "dur": 66, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669574391, "dur": 1, "ph": "X", "name": "ProcessMessages 314", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669574393, "dur": 73, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669574470, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669574472, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669574535, "dur": 1, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669574537, "dur": 53, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669574595, "dur": 1, "ph": "X", "name": "ProcessMessages 515", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669574599, "dur": 109, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669574715, "dur": 11, "ph": "X", "name": "ProcessMessages 560", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669574728, "dur": 105, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669574837, "dur": 2, "ph": "X", "name": "ProcessMessages 2152", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669574841, "dur": 35, "ph": "X", "name": "ReadAsync 2152", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669574879, "dur": 1, "ph": "X", "name": "ProcessMessages 555", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669574880, "dur": 59, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669574945, "dur": 2, "ph": "X", "name": "ProcessMessages 829", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669574948, "dur": 87, "ph": "X", "name": "ReadAsync 829", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669575039, "dur": 1, "ph": "X", "name": "ProcessMessages 328", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669575043, "dur": 38, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669575088, "dur": 5, "ph": "X", "name": "ProcessMessages 560", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669575095, "dur": 449, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669575547, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669575553, "dur": 86, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669575658, "dur": 58, "ph": "X", "name": "ProcessMessages 2181", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669575717, "dur": 128, "ph": "X", "name": "ReadAsync 2181", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669575853, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669575885, "dur": 1, "ph": "X", "name": "ProcessMessages 583", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669575886, "dur": 103, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669575993, "dur": 1, "ph": "X", "name": "ProcessMessages 61", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669575996, "dur": 42, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669576047, "dur": 1, "ph": "X", "name": "ProcessMessages 1337", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669576049, "dur": 37, "ph": "X", "name": "ReadAsync 1337", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669576090, "dur": 1, "ph": "X", "name": "ProcessMessages 233", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669576092, "dur": 29, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669576129, "dur": 1, "ph": "X", "name": "ProcessMessages 480", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669576131, "dur": 35, "ph": "X", "name": "ReadAsync 480", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669576169, "dur": 1, "ph": "X", "name": "ProcessMessages 220", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669576181, "dur": 31, "ph": "X", "name": "ReadAsync 220", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669576214, "dur": 1, "ph": "X", "name": "ProcessMessages 445", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669576216, "dur": 54, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669576273, "dur": 1, "ph": "X", "name": "ProcessMessages 535", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669576276, "dur": 243, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669576524, "dur": 64, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669576594, "dur": 1, "ph": "X", "name": "ProcessMessages 1623", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669576596, "dur": 44, "ph": "X", "name": "ReadAsync 1623", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669576647, "dur": 114, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669576765, "dur": 1, "ph": "X", "name": "ProcessMessages 334", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669576767, "dur": 163, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669576939, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669576979, "dur": 1, "ph": "X", "name": "ProcessMessages 622", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669576980, "dur": 27, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669577016, "dur": 44, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669577065, "dur": 39, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669577108, "dur": 42, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669577152, "dur": 162, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669577319, "dur": 1, "ph": "X", "name": "ProcessMessages 327", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669577321, "dur": 56, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669577394, "dur": 2, "ph": "X", "name": "ProcessMessages 1484", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669577397, "dur": 272, "ph": "X", "name": "ReadAsync 1484", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669577680, "dur": 2421, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669580109, "dur": 4, "ph": "X", "name": "ProcessMessages 2320", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669580114, "dur": 555, "ph": "X", "name": "ReadAsync 2320", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669580675, "dur": 17, "ph": "X", "name": "ProcessMessages 18077", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669580694, "dur": 42, "ph": "X", "name": "ReadAsync 18077", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669580738, "dur": 1, "ph": "X", "name": "ProcessMessages 523", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669580741, "dur": 183, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669580927, "dur": 2, "ph": "X", "name": "ProcessMessages 988", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669580930, "dur": 42, "ph": "X", "name": "ReadAsync 988", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669580975, "dur": 1, "ph": "X", "name": "ProcessMessages 578", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669580977, "dur": 102, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669581082, "dur": 1, "ph": "X", "name": "ProcessMessages 313", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669581085, "dur": 214, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669581302, "dur": 2, "ph": "X", "name": "ProcessMessages 2048", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669581306, "dur": 52, "ph": "X", "name": "ReadAsync 2048", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669581361, "dur": 1, "ph": "X", "name": "ProcessMessages 1605", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669581363, "dur": 59, "ph": "X", "name": "ReadAsync 1605", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669581429, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669581433, "dur": 298, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669581734, "dur": 1, "ph": "X", "name": "ProcessMessages 985", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669581737, "dur": 208, "ph": "X", "name": "ReadAsync 985", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669581948, "dur": 2, "ph": "X", "name": "ProcessMessages 1065", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669581952, "dur": 194, "ph": "X", "name": "ReadAsync 1065", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669582149, "dur": 2, "ph": "X", "name": "ProcessMessages 1335", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669582153, "dur": 46, "ph": "X", "name": "ReadAsync 1335", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669582205, "dur": 2, "ph": "X", "name": "ProcessMessages 289", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669582209, "dur": 207, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669582420, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669582422, "dur": 94, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669582519, "dur": 2, "ph": "X", "name": "ProcessMessages 1863", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669582523, "dur": 94, "ph": "X", "name": "ReadAsync 1863", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669582620, "dur": 1, "ph": "X", "name": "ProcessMessages 109", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669582622, "dur": 39, "ph": "X", "name": "ReadAsync 109", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669582664, "dur": 1, "ph": "X", "name": "ProcessMessages 545", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669582666, "dur": 43, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669582712, "dur": 1, "ph": "X", "name": "ProcessMessages 310", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669582714, "dur": 95, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669582812, "dur": 1, "ph": "X", "name": "ProcessMessages 141", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669582815, "dur": 194, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669583013, "dur": 13, "ph": "X", "name": "ProcessMessages 1717", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669583027, "dur": 34, "ph": "X", "name": "ReadAsync 1717", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669583072, "dur": 1, "ph": "X", "name": "ProcessMessages 236", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669583075, "dur": 808, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669583886, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669583888, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669583919, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669583921, "dur": 32, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669583958, "dur": 51, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669584013, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669584036, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669584057, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669584077, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669584098, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669584119, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669584139, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669584162, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669584189, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669584216, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669584246, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669584269, "dur": 48, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669584320, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669584344, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669584366, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669584387, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669584415, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669584436, "dur": 23, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669584463, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669584483, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669584503, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669584523, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669584544, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669584578, "dur": 32, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669584613, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669584645, "dur": 17, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669584666, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669584703, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669584741, "dur": 24, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669584769, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669584790, "dur": 23, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669584816, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669584836, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669584856, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669584876, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669584900, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669584920, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669584940, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669584962, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669584982, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669585002, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669585025, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669585045, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669585076, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669585096, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669585115, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669585135, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669585156, "dur": 16, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669585174, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669585176, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669585197, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669585217, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669585237, "dur": 27, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669585266, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669585292, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669585313, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669585333, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669585353, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669585372, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669585399, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669585419, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669585443, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669585464, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669585483, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669585511, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669585532, "dur": 21, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669585556, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669585576, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669585602, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669585622, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669585652, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669585673, "dur": 30, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669585706, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669585726, "dur": 57, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669585786, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669585817, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669585844, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669585866, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669585886, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669585914, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669585940, "dur": 1585, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669587531, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669587534, "dur": 61, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669587597, "dur": 7, "ph": "X", "name": "ProcessMessages 1152", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669587605, "dur": 72, "ph": "X", "name": "ReadAsync 1152", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669587689, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669587719, "dur": 27, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669587749, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669587777, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669587779, "dur": 34, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669587817, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669587819, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669587848, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669587850, "dur": 21, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669587874, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669587905, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669587927, "dur": 139, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669588070, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669588099, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669588101, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669588130, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669588153, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669588172, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669588194, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669588218, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669588245, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669588286, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669588310, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669588334, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669588355, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669588380, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669588406, "dur": 35, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669588444, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669588468, "dur": 19, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669588490, "dur": 114, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669588608, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669588633, "dur": 101, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669588738, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669588761, "dur": 48, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669588813, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669588838, "dur": 48, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669588889, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669588913, "dur": 98, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669589014, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669589040, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669589060, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669589081, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669589103, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669589124, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669589144, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669589166, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669589188, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669589209, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669589230, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669589258, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669589300, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669589322, "dur": 80, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669589406, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669589408, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669589435, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669589462, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669589486, "dur": 18, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669589507, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669589530, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669589550, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669589569, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669589591, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669589625, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669589645, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669589673, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669589695, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669589715, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669589738, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669589764, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669589786, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669589809, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669589812, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669589835, "dur": 101, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669589940, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669589965, "dur": 70, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669590040, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669590063, "dur": 41, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669590107, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669590131, "dur": 1117, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669591253, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669591255, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669591299, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669591301, "dur": 700, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669592005, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669592007, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669592039, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669592045, "dur": 1977, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669594027, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669594030, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669594082, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669594084, "dur": 36, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669594124, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669594126, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669594165, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669594192, "dur": 2431, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669596631, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669596635, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669596688, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669596691, "dur": 143, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669596841, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669596874, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669596876, "dur": 494, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669597375, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669597378, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669597407, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669597409, "dur": 438, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669597851, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669597853, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669597887, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669597891, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669597919, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669597947, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669597977, "dur": 53, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669598033, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669598060, "dur": 703, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669598768, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669598770, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669598808, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669598810, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669598842, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669598878, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669598912, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669598914, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669598941, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669598943, "dur": 48, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669598994, "dur": 9, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669599004, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669599027, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669599029, "dur": 153, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669599186, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669599209, "dur": 19, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669599232, "dur": 448, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669599683, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669599685, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669599714, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669599735, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669599759, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669599781, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669599801, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669599822, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669599843, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669599865, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669599894, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669599917, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669599940, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669599964, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669599991, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669600012, "dur": 156, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669600173, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669600179, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669600207, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669600209, "dur": 33, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669600245, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669600274, "dur": 49, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669600326, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669600352, "dur": 297, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669600653, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669600655, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669600682, "dur": 56, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669600742, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669600769, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669600771, "dur": 308, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669601085, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669601087, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669601134, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669601136, "dur": 583, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669601725, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669601727, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669601761, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669601790, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669601817, "dur": 174, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669601996, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669601998, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669602032, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350669602034, "dur": 507822, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350670109865, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350670109869, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350670109906, "dur": 66, "ph": "X", "name": "ProcessMessages 383", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350670109974, "dur": 5129, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350670115112, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350670115116, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350670115165, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350670115170, "dur": 1980, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350670117171, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350670117175, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350670117221, "dur": 24, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350670117246, "dur": 123225, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350670240481, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350670240486, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350670240534, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350670240538, "dur": 1377, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350670241925, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350670241930, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350670241983, "dur": 31, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350670242017, "dur": 17755, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350670259785, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350670259789, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350670259840, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350670259846, "dur": 299, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350670260154, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350670260204, "dur": 9, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350670260215, "dur": 308372, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350670568597, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350670568602, "dur": 71, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350670568677, "dur": 47, "ph": "X", "name": "ProcessMessages 375", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350670568727, "dur": 3207, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350670571942, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350670571946, "dur": 94, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350670572043, "dur": 11, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350670572055, "dur": 1718, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350670573782, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350670573785, "dur": 79, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350670573872, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350670573878, "dur": 673, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350670574557, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350670574561, "dur": 79, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350670574647, "dur": 38, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350670574688, "dur": 51813, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350670626513, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350670626518, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350670626561, "dur": 26, "ph": "X", "name": "ProcessMessages 1193", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350670626589, "dur": 4329, "ph": "X", "name": "ReadAsync 1193", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350670630930, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350670630934, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350670630992, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350670630998, "dur": 1230, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350670632236, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350670632239, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350670632283, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350670632287, "dur": 24, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350670632314, "dur": 21, "ph": "X", "name": "ProcessMessages 46", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350670632337, "dur": 1379, "ph": "X", "name": "ReadAsync 46", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350670633728, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350670633731, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350670633760, "dur": 25, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350670633787, "dur": 28608, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350670662406, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350670662411, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350670662476, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350670662482, "dur": 2546, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350670665038, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350670665043, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350670665106, "dur": 28, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350670665135, "dur": 107950, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350670773096, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350670773112, "dur": 144, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350670773262, "dur": 23, "ph": "X", "name": "ProcessMessages 960", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350670773288, "dur": 11077, "ph": "X", "name": "ReadAsync 960", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350670784372, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350670784375, "dur": 186, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350670784565, "dur": 1, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 7132, "tid": 85899345920, "ts": 1750350670784567, "dur": 10195, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 7132, "tid": 97, "ts": 1750350670796717, "dur": 1722, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 7132, "tid": 81604378624, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 7132, "tid": 81604378624, "ts": 1750350669537959, "dur": 47992, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 7132, "tid": 81604378624, "ts": 1750350669585952, "dur": 89, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 7132, "tid": 97, "ts": 1750350670798443, "dur": 13, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 7132, "tid": 77309411328, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 7132, "tid": 77309411328, "ts": 1750350669530621, "dur": 1264309, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 7132, "tid": 77309411328, "ts": 1750350669531278, "dur": 6483, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 7132, "tid": 77309411328, "ts": 1750350670794983, "dur": 219, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 7132, "tid": 77309411328, "ts": 1750350670795052, "dur": 80, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 7132, "tid": 77309411328, "ts": 1750350670795207, "dur": 1, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 7132, "tid": 97, "ts": 1750350670798459, "dur": 74, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1750350669554608, "dur": 140, "ph": "X", "name": "IPC_Client_InitializeAndConnectToParent", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750350669554791, "dur": 4017, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750350669559030, "dur": 5642, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750350669564746, "dur": 1210, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750350669566177, "dur": 391, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ScriptAssemblies"}}, {"pid": 12345, "tid": 0, "ts": 1750350669566626, "dur": 126, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ClothModule.dll_D461DBDF1BF3D038.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750350669566830, "dur": 126, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_416ADE92277E457E.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750350669567205, "dur": 133, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_F591B0E4ADD29A56.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750350669567401, "dur": 128, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_CB9C52C6DA4AB85A.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750350669567656, "dur": 151, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_1AB2600799DC5DD4.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750350669567875, "dur": 123, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_A02BE6282932C1CB.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750350669568061, "dur": 130, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_AC1145378078C087.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750350669568385, "dur": 121, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_F0A87365319D71DD.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750350669568558, "dur": 124, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_D0F1A61EB4189FC9.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750350669568763, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IdentifiersModule.dll_908A7CF0F95B5EED.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750350669568923, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_5E1525907236878A.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750350669569026, "dur": 132, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_8F9DC40C34B6425C.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750350669569268, "dur": 546, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_D1622255EC84C8DF.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750350669569895, "dur": 145, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_EF62AA7CD312F942.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750350669570108, "dur": 133, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_AE7AE761007ACD6C.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750350669570321, "dur": 201, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_4B6EE49D6AF37933.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750350669570563, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_E0FCC25CAA3CC2C8.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750350669571414, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1750350669571893, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1750350669572051, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1750350669573166, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Cinemachine.ref.dll_77EC0F35C376A022.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750350669573677, "dur": 102, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750350669574836, "dur": 145, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualStudio.Editor.ref.dll_3A975DBA53ABA4AD.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750350669576385, "dur": 653, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Abilities.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1750350669577042, "dur": 433, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Abilities.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1750350669577553, "dur": 276, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/TacticalCombatSystem.Abilities.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1750350669577923, "dur": 145, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Characters.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1750350669578243, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Actions.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1750350669578467, "dur": 205, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Camera.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1750350669578696, "dur": 139, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Camera.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750350669578893, "dur": 157, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1750350669579111, "dur": 144, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1750350669579581, "dur": 108, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1750350669579974, "dur": 141, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1750350669565990, "dur": 14132, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750350669580131, "dur": 1193577, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750350670773710, "dur": 378, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750350670774107, "dur": 136, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750350670781322, "dur": 72, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750350670781413, "dur": 3330, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1750350669566167, "dur": 13977, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750350669580162, "dur": 621, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_EF62AA7CD312F942.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750350669581010, "dur": 447, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_EF62AA7CD312F942.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750350669581458, "dur": 1774, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_D910E96BA6DA4E34.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750350669583553, "dur": 439, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1750350669584080, "dur": 510, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1750350669584702, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1750350669584777, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1750350669584884, "dur": 259, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1750350669585144, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750350669585271, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1750350669585467, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750350669585626, "dur": 426, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Characters.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1750350669586053, "dur": 488, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750350669586541, "dur": 267, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Characters.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1750350669586895, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Battle.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1750350669587011, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750350669587074, "dur": 1257, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Battle.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750350669588396, "dur": 898, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750350669589295, "dur": 630, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750350669589925, "dur": 624, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750350669591156, "dur": 911, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.cinemachine@b66fdb7cd1f2\\Runtime\\Core\\BlendManager.cs"}}, {"pid": 12345, "tid": 1, "ts": 1750350669590549, "dur": 1518, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750350669592067, "dur": 522, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750350669592590, "dur": 424, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750350669593207, "dur": 921, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@b10ae3e6f84c\\UnityEditor.TestRunner\\Api\\TestAdaptor.cs"}}, {"pid": 12345, "tid": 1, "ts": 1750350669593014, "dur": 1417, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750350669594529, "dur": 891, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750350669595435, "dur": 613, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750350669596049, "dur": 51, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750350669596100, "dur": 75, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750350669596175, "dur": 865, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750350669597041, "dur": 98, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750350669597139, "dur": 175, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750350669597317, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750350669597380, "dur": 438, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750350669597873, "dur": 986, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750350669598917, "dur": 513166, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750350670112084, "dur": 456654, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750350670568824, "dur": 165, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\TacticalCombatSystem.Status.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1750350670568740, "dur": 250, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/TacticalCombatSystem.Status.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1750350670569015, "dur": 2666, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/TacticalCombatSystem.Status.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1750350670571693, "dur": 202091, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750350669566172, "dur": 13990, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750350669580170, "dur": 606, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_163A7D00BA7D065C.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750350669581046, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_BF8B01E2682946F2.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750350669581206, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_0399CE90B9A515B4.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750350669581364, "dur": 539, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750350669581908, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_9A8E98434D7100DD.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750350669582037, "dur": 879, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750350669582921, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_F5DF255C08D11A8C.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750350669582991, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750350669583079, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_A4F1D8B2044EDCB1.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750350669583514, "dur": 329, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1750350669583875, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750350669583995, "dur": 423, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1750350669584527, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Status.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750350669584669, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750350669584786, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750350669584935, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750350669584995, "dur": 6046, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1750350669591042, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750350669591158, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750350669591226, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750350669591312, "dur": 690, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750350669592002, "dur": 776, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750350669592778, "dur": 544, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750350669593323, "dur": 402, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750350669593725, "dur": 997, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750350669594722, "dur": 478, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750350669595200, "dur": 132, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750350669595453, "dur": 559, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750350669596012, "dur": 96, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750350669596142, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750350669596317, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750350669596369, "dur": 431, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1750350669596801, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750350669597034, "dur": 104, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750350669597138, "dur": 194, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750350669597332, "dur": 522, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750350669597854, "dur": 1009, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750350669598864, "dur": 76, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750350669598941, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750350669599120, "dur": 512790, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750350670112331, "dur": 144152, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\TacticalCombatSystem.Interfaces.ref.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750350670111913, "dur": 144580, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Battle.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1750350670257150, "dur": 54, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750350670258553, "dur": 365011, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Battle.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1750350670628965, "dur": 30475, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\TacticalCombatSystem.Battle.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750350670628940, "dur": 30501, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/TacticalCombatSystem.Battle.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750350670659462, "dur": 2518, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/TacticalCombatSystem.Battle.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750350670661985, "dur": 111762, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750350669566215, "dur": 13964, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750350669580183, "dur": 626, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_CE52468F2AF94D3E.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750350669581008, "dur": 382, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_CE52468F2AF94D3E.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750350669581391, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_49461019CED446C6.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750350669581476, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750350669581555, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_D23AD71C5DDFE1B4.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750350669581686, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750350669581772, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_F0A87365319D71DD.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750350669581836, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750350669582723, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750350669582800, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_747257F2B95C7621.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750350669582976, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750350669583492, "dur": 424, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1750350669583948, "dur": 399, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1750350669584389, "dur": 10506, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1750350669594896, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750350669595005, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750350669595168, "dur": 646, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1750350669595815, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750350669595945, "dur": 364, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750350669596343, "dur": 517, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1750350669596860, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750350669597003, "dur": 145, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750350669597148, "dur": 171, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750350669597322, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750350669597385, "dur": 402, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750350669597792, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750350669597863, "dur": 1002, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750350669598865, "dur": 513239, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750350670112104, "dur": 456659, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750350670568764, "dur": 205018, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750350669566310, "dur": 13992, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750350669580315, "dur": 392, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_9B5BF5B045BFFED8.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750350669581037, "dur": 852, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_D1622255EC84C8DF.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750350669582842, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750350669582984, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_E90840FC7BA4DF8A.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750350669584274, "dur": 332, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.2.0b2/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 4, "ts": 1750350669583186, "dur": 1420, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750350669584634, "dur": 266, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1750350669584951, "dur": 288, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1750350669585520, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1750350669585732, "dur": 264, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1750350669586027, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Abilities.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750350669586364, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Actions.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750350669586535, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Actions.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750350669586619, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.UI.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1750350669586872, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750350669586931, "dur": 200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1750350669587178, "dur": 645, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750350669587824, "dur": 817, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750350669588641, "dur": 491, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750350669589132, "dur": 677, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750350669589810, "dur": 869, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750350669590680, "dur": 934, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750350669591614, "dur": 642, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750350669592325, "dur": 1108, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@b10ae3e6f84c\\UnityEditor.TestRunner\\TestRun\\Tasks\\CleanupVerificationTask.cs"}}, {"pid": 12345, "tid": 4, "ts": 1750350669594048, "dur": 731, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@b10ae3e6f84c\\UnityEditor.TestRunner\\TestResultSerializer.cs"}}, {"pid": 12345, "tid": 4, "ts": 1750350669592256, "dur": 2550, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750350669594806, "dur": 350, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750350669595157, "dur": 612, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750350669595769, "dur": 179, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750350669595951, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750350669596017, "dur": 72, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750350669596141, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750350669596338, "dur": 466, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1750350669596804, "dur": 259, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750350669597117, "dur": 248, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750350669597365, "dur": 475, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750350669597841, "dur": 1031, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750350669598872, "dur": 513224, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750350670112097, "dur": 456614, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750350670568776, "dur": 59139, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\TacticalCombatSystem.Status.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750350670568713, "dur": 59204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/TacticalCombatSystem.Status.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750350670627951, "dur": 1473, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/TacticalCombatSystem.Status.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750350670629428, "dur": 144351, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750350669566258, "dur": 13952, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750350669580227, "dur": 476, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_16F59DAED9B12573.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750350669580708, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750350669581380, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750350669581844, "dur": 1478, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_6648D96BDBBB29C0.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750350669583482, "dur": 349, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1750350669583832, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750350669583898, "dur": 294, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1750350669584233, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750350669584545, "dur": 325, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Status.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1750350669584968, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750350669585045, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750350669585201, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750350669585313, "dur": 255, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1750350669585600, "dur": 328, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Abilities.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1750350669585966, "dur": 296, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1750350669586378, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750350669586604, "dur": 219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Camera.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1750350669586923, "dur": 246, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Battle.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1750350669587170, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750350669587245, "dur": 467, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750350669587712, "dur": 732, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750350669588444, "dur": 642, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750350669589087, "dur": 634, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750350669589721, "dur": 671, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750350669590392, "dur": 596, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750350669590988, "dur": 772, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750350669591760, "dur": 900, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750350669592661, "dur": 846, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750350669594078, "dur": 793, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.2.0b2\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.Duplex.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750350669593508, "dur": 1774, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750350669595438, "dur": 596, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750350669596034, "dur": 57, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750350669596091, "dur": 101, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750350669596192, "dur": 735, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750350669596937, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750350669597041, "dur": 87, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750350669597128, "dur": 229, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750350669597357, "dur": 498, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750350669597855, "dur": 1006, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750350669598866, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750350669598925, "dur": 512989, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750350670112022, "dur": 125512, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\TacticalCombatSystem.Interfaces.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750350670111916, "dur": 125620, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/TacticalCombatSystem.Interfaces.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750350670237564, "dur": 1460, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/TacticalCombatSystem.Interfaces.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750350670239029, "dur": 329719, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750350670568748, "dur": 204997, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750350669566337, "dur": 13980, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750350669580324, "dur": 451, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_2A38684436712F05.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750350669581064, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_DB43258EFB737D47.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750350669581385, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_9A1C02F8E98C469D.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750350669581550, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750350669581693, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_F9164AECFF7EBE63.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750350669581793, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750350669581876, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_1FC85B3DDABA9491.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750350669581984, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750350669582083, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_A36FF8077F8E7B00.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750350669582210, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750350669582360, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_4FF2CB3C17FB8E73.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750350669582516, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750350669582660, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_31E341F8DD700DC8.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750350669582788, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750350669582854, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_416ADE92277E457E.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750350669583081, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_9F4733395C159688.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750350669583394, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750350669583548, "dur": 553, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1750350669584180, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Interfaces.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750350669584317, "dur": 566, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1750350669584883, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750350669584964, "dur": 468, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Core.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1750350669585432, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750350669585593, "dur": 251, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Characters.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750350669585949, "dur": 343, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1750350669586293, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750350669586437, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Camera.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750350669586722, "dur": 200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1750350669586949, "dur": 451, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750350669587400, "dur": 424, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750350669587824, "dur": 841, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750350669588666, "dur": 627, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750350669589294, "dur": 624, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750350669589919, "dur": 648, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750350669591204, "dur": 1146, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.cinemachine@b66fdb7cd1f2\\Runtime\\Behaviours\\CinemachineSequencerCamera.cs"}}, {"pid": 12345, "tid": 6, "ts": 1750350669590568, "dur": 1782, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750350669592351, "dur": 797, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750350669593302, "dur": 514, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@b10ae3e6f84c\\UnityEditor.TestRunner\\Api\\CallbacksHolder.cs"}}, {"pid": 12345, "tid": 6, "ts": 1750350669594092, "dur": 790, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.2.0b2\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Numerics.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750350669593148, "dur": 1844, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750350669595086, "dur": 710, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750350669595797, "dur": 207, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750350669596143, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750350669596354, "dur": 379, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1750350669596734, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750350669596910, "dur": 141, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750350669597077, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750350669597130, "dur": 210, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750350669597340, "dur": 506, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750350669597846, "dur": 1024, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750350669598870, "dur": 513225, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750350670112095, "dur": 456651, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750350670568747, "dur": 60199, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750350670629000, "dur": 157, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\TacticalCombatSystem.Battle.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1750350670628948, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/TacticalCombatSystem.Battle.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1750350670629169, "dur": 125, "ph": "X", "name": "EmitNodeStart", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750350670629297, "dur": 1540, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/TacticalCombatSystem.Battle.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1750350670630841, "dur": 142940, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750350669566368, "dur": 13986, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750350669580358, "dur": 443, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_D437D5B52E940013.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750350669580802, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750350669581048, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_564C15F1D50DDD04.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750350669581229, "dur": 253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_167AF4DBC06E2AE6.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750350669581863, "dur": 1516, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750350669583387, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_361A65A56C957CE2.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750350669583689, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1750350669583876, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1750350669584251, "dur": 334, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Interfaces.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1750350669584610, "dur": 3858, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Interfaces.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1750350669588473, "dur": 74, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750350669589344, "dur": 517963, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Interfaces.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1750350670112256, "dur": 144209, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\TacticalCombatSystem.Interfaces.ref.dll"}}, {"pid": 12345, "tid": 7, "ts": 1750350670111908, "dur": 144566, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Status.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1750350670256796, "dur": 53, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750350670258625, "dur": 307053, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Status.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1750350670568997, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\TacticalCombatSystem.Status.ref.dll"}}, {"pid": 12345, "tid": 7, "ts": 1750350670569072, "dur": 1064, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\TacticalCombatSystem.Core.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1750350670568711, "dur": 1427, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1750350670570144, "dur": 139, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750350670570381, "dur": 199713, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1750350669566400, "dur": 13964, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750350669580365, "dur": 344, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_F78CC16BE3410205.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750350669580710, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750350669581227, "dur": 947, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_1BC2F9751197887F.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750350669582175, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750350669582292, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_1AB2600799DC5DD4.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750350669582441, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750350669582556, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_F591B0E4ADD29A56.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750350669582679, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750350669582822, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750350669583155, "dur": 390, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_BFE30F898F3B693C.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750350669583547, "dur": 450, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1750350669584032, "dur": 9619, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1750350669593653, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750350669593787, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750350669593970, "dur": 1986, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1750350669595957, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750350669596140, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750350669596325, "dur": 559, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750350669596892, "dur": 345, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1750350669597237, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750350669597339, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750350669597459, "dur": 260, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1750350669597785, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750350669597889, "dur": 227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750350669598117, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750350669598198, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750350669598254, "dur": 485, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1750350669598741, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750350669598866, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750350669598936, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750350669599126, "dur": 512905, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750350670112069, "dur": 99, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\TacticalCombatSystem.Interfaces.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1750350670112033, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/TacticalCombatSystem.Interfaces.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1750350670112221, "dur": 2046, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/TacticalCombatSystem.Interfaces.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1750350670114271, "dur": 454485, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750350670568757, "dur": 204918, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750350670788910, "dur": 2234, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 7132, "tid": 97, "ts": 1750350670799038, "dur": 7307, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 7132, "tid": 97, "ts": 1750350670806438, "dur": 2345, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 7132, "tid": 97, "ts": 1750350670796540, "dur": 12353, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}