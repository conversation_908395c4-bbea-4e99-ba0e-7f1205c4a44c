using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using TMPro;
using TacticalCombatSystem.Characters;
using TacticalCombatSystem.Interfaces;
using TacticalCombatSystem.Interfaces.Core;
using TacticalCombatSystem.Battle;

namespace TacticalCombatSystem.UI
{
    /// <summary>
    /// Manages the battle UI elements and handles user input during combat
    /// </summary>
    public class BattleUI : MonoBehaviour, IBattleUI
    {
        [Header("UI Elements")]
        [SerializeField] private GameObject battleMenu;
        [SerializeField] private Button attackButton;
        [SerializeField] private Button abilityButton;
        [SerializeField] private Button itemButton;
        [SerializeField] private Button defendButton;
        [SerializeField] private TextMeshProUG<PERSON> turnText;
        [SerializeField] private TextMeshPro<PERSON>G<PERSON> battleLogText;
        [SerializeField] private Transform playerTeamPanel;
        [SerializeField] private Transform enemyTeamPanel;
        [SerializeField] private Transform abilityPanel;
        [SerializeField] private GameObject abilityButtonPrefab;
        [SerializeField] private GameObject targetSelectionPanel;
        [SerializeField] private Transform targetButtonContainer;
        [SerializeField] private GameObject targetButtonPrefab;
        
        [Header("Prefabs")]
        [SerializeField] private GameObject characterUIPrefab;
        
        // References
        private IBattleManager battleManager;
        private BaseCharacter currentCharacter;
        private Dictionary<BaseCharacter, CharacterUI> characterUIs = new Dictionary<BaseCharacter, CharacterUI>();
        private List<BaseCharacter> currentTargets = new List<BaseCharacter>();
        private Ability selectedAbility;
        
        private void Awake()
        {
            battleManager = BattleManager.Instance;
            if (battleManager == null)
            {
                Debug.LogError("BattleManager not found in the scene!");
                return;
            }
            
            // Initialize UI
            battleMenu.SetActive(false);
            targetSelectionPanel.SetActive(false);
            
            if (abilityPanel != null) abilityPanel.gameObject.SetActive(false);
        }
        
        private void Start()
        {
            // Subscribe to battle events
            battleManager.OnBattleStarted += OnBattleStarted;
            battleManager.OnTurnStarted += OnTurnStarted;
            battleManager.OnCharacterDamaged += OnCharacterDamaged;
            battleManager.OnCharacterHealed += OnCharacterHealed;
            battleManager.OnStatusEffectApplied += OnStatusEffectApplied;
            battleManager.OnBattleEnded += OnBattleEnded;
            
            // Set up button listeners
            if (attackButton != null) attackButton.onClick.AddListener(OnAttackButtonClicked);
            if (abilityButton != null) abilityButton.onClick.AddListener(OnAbilityButtonClicked);
            if (itemButton != null) itemButton.onClick.AddListener(OnItemButtonClicked);
            if (defendButton != null) defendButton.onClick.AddListener(OnDefendButtonClicked);
        }
        
        private void OnDestroy()
        {
            // Unsubscribe from events
            if (battleManager != null)
            {
                battleManager.OnBattleStarted -= OnBattleStarted;
                battleManager.OnTurnStarted -= OnTurnStarted;
                battleManager.OnCharacterDamaged -= OnCharacterDamaged;
                battleManager.OnCharacterHealed -= OnCharacterHealed;
                battleManager.OnStatusEffectApplied -= OnStatusEffectApplied;
                battleManager.OnBattleEnded -= OnBattleEnded;
            }
        }
        
        private void OnBattleStarted()
        {
            // Initialize UI when battle starts
            if (battleManager != null)
            {
                var playerTeam = battleManager.GetPlayerTeam();
                var enemyTeam = battleManager.GetEnemyTeam();

                InitializeUI(playerTeam, enemyTeam);
            }
        }
        
        private void OnTurnStarted(ICombatParticipant participant)
        {
            if (!(participant is BaseCharacter character)) return;
            
            currentCharacter = character;
            
            // Update turn text
            if (turnText != null)
            {
                turnText.text = $"{character.CharacterName}'s Turn";
            }
            
            // Show/hide battle menu based on if it's player's turn
            bool isPlayerTurn = character.IsPlayerControlled;
            battleMenu.SetActive(isPlayerTurn);
            
            // If it's an AI turn, let the AI make a decision
            if (!isPlayerTurn)
            {
                StartCoroutine(AITurnCoroutine(character));
            }
            
            // Update all character UIs
            foreach (var ui in characterUIs)
            {
                ui.Value.UpdateUI();
            }
        }
        
        private IEnumerator AITurnCoroutine(BaseCharacter aiCharacter)
        {
            // Simple AI: Just attack a random target
            yield return new WaitForSeconds(1f);
            
            if (battleManager == null || !aiCharacter.IsAlive) yield break;
            
            // Get a random target from the player team
            var playerTeam = battleManager.GetPlayerTeam();
            var aliveTargets = new List<ICombatParticipant>();
            
            foreach (var character in playerTeam)
            {
                if (character.IsAlive)
                    aliveTargets.Add(character);
            }
            
            if (aliveTargets.Count > 0)
            {
                var target = aliveTargets[Random.Range(0, aliveTargets.Count)];
                // Use a basic attack (null ability for basic attack)
                battleManager.PlayerAttack(aiCharacter, target, null);
            }
            
            // End AI turn
            battleManager.EndTurn();
        }
        
        private void OnAttackButtonClicked()
        {
            if (battleManager == null || currentCharacter == null) return;
            
            // Show enemy selection
            foreach (var ui in characterUIs)
            {
                if (!ui.Key.IsPlayerControlled && ui.Key.IsAlive)
                {
                    ui.Value.ShowAsTarget();
                }
            }
        }
        
        private void OnCharacterSelected(BaseCharacter character)
        {
            if (battleManager == null || currentCharacter == null) return;
            
            // Hide all target highlights
            foreach (var ui in characterUIs.Values)
            {
                ui.HideTarget();
            }
            
            // Execute attack with the selected target
            battleManager.PlayerAttack(currentCharacter, character, selectedAbility);
        }
        
        private void OnAbilityButtonClicked()
        {
            // Show ability selection
            if (abilityPanel != null)
            {
                abilityPanel.gameObject.SetActive(true);
                // TODO: Populate ability buttons
            }
        }
        
        private void OnItemButtonClicked()
        {
            // Show item selection
            Debug.Log("Item button clicked");
            // TODO: Implement item selection UI
        }
        
        private void OnDefendButtonClicked()
        {
            // Defend action
            if (battleManager != null && currentCharacter != null)
            {
                // TODO: Implement defend action in BattleManager
                battleManager.EndTurn();
            }
        }
        
        private void OnCharacterDamaged(ICombatParticipant character, int damage)
        {
            // Update character UI
            if (character is BaseCharacter baseChar && characterUIs.TryGetValue(baseChar, out var charUI))
            {
                charUI.UpdateUI();
            }
            
            // Add to battle log
            if (battleLogText != null)
            {
                battleLogText.text = $"{character.Name} took {damage} damage!\n" + battleLogText.text;
            }
        }
        
        private void OnCharacterHealed(ICombatParticipant character, int amount)
        {
            // Update character UI
            if (character is BaseCharacter baseChar && characterUIs.TryGetValue(baseChar, out var charUI))
            {
                charUI.UpdateUI();
            }
            
            // Add to battle log
            if (battleLogText != null)
            {
                battleLogText.text = $"{character.Name} recovered {amount} HP!\n" + battleLogText.text;
            }
        }
        
        private void OnStatusEffectApplied(ICombatParticipant character, IStatusEffect effect)
        {
            // Update character UI
            if (character is BaseCharacter baseChar && characterUIs.TryGetValue(baseChar, out var charUI))
            {
                charUI.UpdateUI();
            }
            
            // Add to battle log
            if (battleLogText != null)
            {
                battleLogText.text = $"{character.Name} was affected by {effect.Name}!\n" + battleLogText.text;
            }
        }
        
        private void OnBattleEnded(bool playerWon)
        {
            // Show battle end message
            if (turnText != null)
            {
                turnText.text = playerWon ? "Victory!" : "Defeat!";
            }
            
            // Disable battle menu
            battleMenu.SetActive(false);
        }
        
        /// <summary>
        /// Initialize the UI with the battle teams
        /// </summary>
        public void InitializeUI(List<ICombatParticipant> playerTeam, List<ICombatParticipant> enemyTeam)
        {
            // Convert ICombatParticipant to BaseCharacter for internal use
            var playerChars = new List<BaseCharacter>();
            var enemyChars = new List<BaseCharacter>();

            foreach (var participant in playerTeam)
            {
                if (participant is BaseCharacter baseChar)
                    playerChars.Add(baseChar);
            }

            foreach (var participant in enemyTeam)
            {
                if (participant is BaseCharacter baseChar)
                    enemyChars.Add(baseChar);
            }

            InitializeUIInternal(playerChars, enemyChars);
        }

        /// <summary>
        /// Internal method for initializing UI with BaseCharacter lists
        /// </summary>
        private void InitializeUIInternal(List<BaseCharacter> playerTeam, List<BaseCharacter> enemyTeam)
        {
            // Clear existing UI
            ClearPanel(playerTeamPanel);
            ClearPanel(enemyTeamPanel);
            
            characterUIs.Clear();
            
            // Create UI for player team
            foreach (var character in playerTeam)
            {
                CreateCharacterUI(character, playerTeamPanel);
            }
            
            // Create UI for enemy team
            foreach (var character in enemyTeam)
            {
                CreateCharacterUI(character, enemyTeamPanel);
            }
            
            // Hide battle menu initially
            battleMenu.SetActive(false);
        }
        
        private void ClearPanel(Transform panel)
        {
            if (panel == null) return;
            
            foreach (Transform child in panel)
            {
                Destroy(child.gameObject);
            }
        }
        
        private void CreateCharacterUI(BaseCharacter character, Transform parent)
        {
            if (character == null || characterUIPrefab == null || parent == null) return;

            var uiObject = Instantiate(characterUIPrefab, parent);
            var characterUI = uiObject.GetComponent<CharacterUI>();

            if (characterUI != null)
            {
                characterUI.Initialize(character);
                characterUIs[character] = characterUI;

                // Add button functionality for selecting targets
                var button = uiObject.GetComponent<Button>();
                if (button != null)
                {
                    button.onClick.AddListener(() => OnCharacterSelected(character));
                }
            }
        }

        // IBattleUI interface implementation
        public void SetBattleMenuActive(bool active)
        {
            if (battleMenu != null)
                battleMenu.SetActive(active);
        }

        public void UpdateTurnDisplay(string turnText)
        {
            if (this.turnText != null)
                this.turnText.text = turnText;
        }

        public void AddToBattleLog(string message)
        {
            if (battleLogText != null)
            {
                battleLogText.text = message + "\n" + battleLogText.text;
            }
        }

        public void ShowBattleEndMessage(bool playerWon)
        {
            if (turnText != null)
            {
                turnText.text = playerWon ? "Victory!" : "Defeat!";
            }

            SetBattleMenuActive(false);
        }
    }
}
